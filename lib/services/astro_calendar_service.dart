import 'package:flutter/material.dart';
import 'package:uuid/uuid.dart';

import '../models/astro_event.dart';
import '../models/birth_data.dart';
import '../models/planet_position.dart';
import '../ui/AppTheme.dart';
import '../utils/LoggerUtils.dart';
import 'astrology_service.dart';
import 'equinox_solstice_service.dart';

/// 星象日曆服務
/// 負責計算和提供日曆相關的星象事件
class AstroCalendarService {
  static final AstroCalendarService _instance = AstroCalendarService._internal();
  factory AstroCalendarService() => _instance;
  AstroCalendarService._internal();

  final EquinoxSolsticeService _equinoxService = EquinoxSolsticeService();
  final AstrologyService _astrologyService = AstrologyService();
  final Uuid _uuid = const Uuid();

  /// 獲取指定月份的所有星象事件
  Future<List<AstroEvent>> getMonthlyEvents(
    int year,
    int month, {
    double latitude = 25.0,
    double longitude = 121.0,
    BirthData? natalPerson,
  }) async {
    final events = <AstroEvent>[];

    try {
      // 獲取月相事件
      final moonPhaseEvents = await _getMoonPhaseEvents(year, month, latitude, longitude);
      events.addAll(moonPhaseEvents);

      // 獲取節氣事件
      final seasonEvents = await _getSeasonEvents(year, month, latitude, longitude);
      events.addAll(seasonEvents);

      // 獲取行星相位事件
      final aspectEvents = await _getPlanetAspectEvents(year, month, latitude, longitude);
      events.addAll(aspectEvents);

      // 獲取行星換座事件
      final signChangeEvents = await _getPlanetSignChangeEvents(year, month, latitude, longitude);
      events.addAll(signChangeEvents);

      // 獲取日月蝕事件
      final eclipseEvents = await _getEclipseEvents(year, month, latitude, longitude);
      events.addAll(eclipseEvents);

      // 按日期排序
      events.sort((a, b) => a.dateTime.compareTo(b.dateTime));

      logger.i('獲取 $year年$month月 星象事件: ${events.length} 個');
    } catch (e) {
      logger.e('獲取月度星象事件失敗: $e');
    }

    return events;
  }

  /// 獲取指定日期的星象事件
  Future<List<AstroEvent>> getDailyEvents(
    DateTime date, {
    double latitude = 25.0,
    double longitude = 121.0,
    BirthData? natalPerson,
  }) async {
    final events = <AstroEvent>[];

    try {
      // 獲取當日的所有事件
      final monthlyEvents = await getMonthlyEvents(
        date.year,
        date.month,
        latitude: latitude,
        longitude: longitude,
        natalPerson: natalPerson,
      );

      // 篩選當日事件
      final dailyEvents = monthlyEvents.where((event) {
        return event.dateTime.year == date.year &&
            event.dateTime.month == date.month &&
            event.dateTime.day == date.day;
      }).toList();

      events.addAll(dailyEvents);

      logger.d('獲取 ${date.toString().split(' ')[0]} 星象事件: ${events.length} 個');
    } catch (e) {
      logger.e('獲取日度星象事件失敗: $e');
    }

    return events;
  }

  /// 獲取月相事件
  Future<List<AstroEvent>> _getMoonPhaseEvents(
    int year,
    int month,
    double latitude,
    double longitude,
  ) async {
    final events = <AstroEvent>[];

    try {
      // 計算該月的月相
      final startDate = DateTime(year, month, 1);
      final endDate = DateTime(year, month + 1, 0);

      // 簡化的月相計算 - 每7.4天一個月相
      final moonCycle = 29.53; // 月相週期
      final phaseDuration = moonCycle / 4; // 每個主要月相間隔

      // 從月初開始計算
      for (int day = 1; day <= endDate.day; day += 7) {
        final date = DateTime(year, month, day);
        final moonPhase = _calculateMoonPhase(date);

        if (moonPhase != null) {
          events.add(AstroEvent(
            id: _uuid.v4(),
            title: AstroEvent.getMoonPhaseDisplayName(moonPhase),
            description: '月相變化：${AstroEvent.getMoonPhaseDisplayName(moonPhase)}',
            dateTime: date,
            type: AstroEventType.moonPhase,
            color: Colors.indigo,
            icon: AstroEvent.getMoonPhaseIcon(moonPhase),
            importance: moonPhase == MoonPhaseType.newMoon || moonPhase == MoonPhaseType.fullMoon ? 4 : 2,
            additionalData: {'moonPhase': moonPhase},
          ));
        }
      }
    } catch (e) {
      logger.e('計算月相事件失敗: $e');
    }

    return events;
  }

  /// 獲取節氣事件
  Future<List<AstroEvent>> _getSeasonEvents(
    int year,
    int month,
    double latitude,
    double longitude,
  ) async {
    final events = <AstroEvent>[];

    try {
      // 獲取該年的節氣數據
      final seasons = await _equinoxService.calculateSeasonTimes(
        year,
        latitude: latitude,
        longitude: longitude,
      );

      // 篩選該月的節氣
      for (final season in seasons) {
        if (season.dateTime.month == month) {
          events.add(AstroEvent(
            id: _uuid.v4(),
            title: season.seasonType.displayName,
            description: '節氣變化：${season.seasonType.displayName}',
            dateTime: season.dateTime,
            type: AstroEventType.seasonChange,
            color: _getSeasonColor(season.seasonType),
            icon: _getSeasonIcon(season.seasonType),
            importance: 5,
            additionalData: {'seasonType': season.seasonType},
          ));
        }
      }
    } catch (e) {
      logger.e('計算節氣事件失敗: $e');
    }

    return events;
  }

  /// 簡化的月相計算
  MoonPhaseType? _calculateMoonPhase(DateTime date) {
    // 這是一個簡化的月相計算，實際應用中應該使用更精確的天文計算
    final dayOfMonth = date.day;

    if (dayOfMonth <= 2) return MoonPhaseType.newMoon;
    if (dayOfMonth <= 7) return MoonPhaseType.firstQuarter;
    if (dayOfMonth <= 16) return MoonPhaseType.fullMoon;
    if (dayOfMonth <= 23) return MoonPhaseType.lastQuarter;
    if (dayOfMonth <= 30) return MoonPhaseType.newMoon;

    return null;
  }

  /// 獲取節氣顏色
  Color _getSeasonColor(SeasonType seasonType) {
    switch (seasonType) {
      case SeasonType.springEquinox:
        return Colors.green;
      case SeasonType.summerSolstice:
        return AppColors.solarAmber;
      case SeasonType.autumnEquinox:
        return Colors.orange;
      case SeasonType.winterSolstice:
        return Colors.blue;
    }
  }

  /// 獲取節氣圖標
  IconData _getSeasonIcon(SeasonType seasonType) {
    switch (seasonType) {
      case SeasonType.springEquinox:
        return Icons.eco;
      case SeasonType.summerSolstice:
        return Icons.wb_sunny;
      case SeasonType.autumnEquinox:
        return Icons.park;
      case SeasonType.winterSolstice:
        return Icons.ac_unit;
    }
  }

  /// 獲取行星相位事件
  Future<List<AstroEvent>> _getPlanetAspectEvents(
    int year,
    int month,
    double latitude,
    double longitude,
  ) async {
    final events = <AstroEvent>[];

    try {
      // 計算該月重要的行星相位
      final startDate = DateTime(year, month, 1);
      final endDate = DateTime(year, month + 1, 0);

      // 每5天檢查一次重要相位
      for (int day = 1; day <= endDate.day; day += 5) {
        final date = DateTime(year, month, day, 12); // 使用中午時間

        // 計算當日行星位置
        final planets = await _astrologyService.calculatePlanetPositions(
          date,
          latitude: latitude,
          longitude: longitude,
        );

        // 計算相位
        final aspects = _astrologyService.calculateAspects(planets);

        // 篩選重要相位（合相、對分、四分）
        final importantAspects = aspects.where((aspect) {
          return aspect.aspect == '合相' ||
              aspect.aspect == '對分相' ||
              aspect.aspect == '四分相';
        }).toList();

        // 添加重要相位事件
        for (final aspect in importantAspects) {
          // 只添加外行星的重要相位
          if (_isImportantPlanetAspect(aspect.planet1.name, aspect.planet2.name)) {
            events.add(AstroEvent(
              id: _uuid.v4(),
              title: '${aspect.planet1.name}${aspect.aspect}${aspect.planet2.name}',
              description: '${aspect.planet1.name}與${aspect.planet2.name}形成${aspect.aspect}相位',
              dateTime: date,
              type: AstroEventType.planetAspect,
              color: _getAspectColor(aspect.aspect),
              icon: Icons.timeline,
              importance: _getAspectImportance(aspect.aspect),
              additionalData: {
                'aspect': aspect,
                'planet1': aspect.planet1.name,
                'planet2': aspect.planet2.name,
                'aspectType': aspect.aspect,
              },
            ));
          }
        }
      }
    } catch (e) {
      logger.e('計算行星相位事件失敗: $e');
    }

    return events;
  }

  /// 獲取行星換座事件
  Future<List<AstroEvent>> _getPlanetSignChangeEvents(
    int year,
    int month,
    double latitude,
    double longitude,
  ) async {
    final events = <AstroEvent>[];

    try {
      // 計算該月的行星換座
      final startDate = DateTime(year, month, 1);
      final endDate = DateTime(year, month + 1, 0);

      // 每天檢查行星位置變化
      DateTime? previousDate;
      List<PlanetPosition>? previousPlanets;

      for (int day = 1; day <= endDate.day; day++) {
        final date = DateTime(year, month, day, 12);

        final planets = await _astrologyService.calculatePlanetPositions(
          date,
          latitude: latitude,
          longitude: longitude,
        );

        if (previousPlanets != null && previousDate != null) {
          // 檢查行星是否換座
          for (int i = 0; i < planets.length && i < previousPlanets.length; i++) {
            final currentPlanet = planets[i];
            final previousPlanet = previousPlanets[i];

            if (currentPlanet.name == previousPlanet.name &&
                currentPlanet.sign != previousPlanet.sign &&
                _isImportantPlanet(currentPlanet.name)) {
              events.add(AstroEvent(
                id: _uuid.v4(),
                title: '${currentPlanet.name}進入${currentPlanet.sign}',
                description: '${currentPlanet.name}從${previousPlanet.sign}進入${currentPlanet.sign}',
                dateTime: date,
                type: AstroEventType.planetSignChange,
                color: currentPlanet.color,
                icon: Icons.swap_horiz,
                importance: _getPlanetImportance(currentPlanet.name),
                additionalData: {
                  'planet': currentPlanet.name,
                  'fromSign': previousPlanet.sign,
                  'toSign': currentPlanet.sign,
                },
              ));
            }
          }
        }

        previousDate = date;
        previousPlanets = planets;
      }
    } catch (e) {
      logger.e('計算行星換座事件失敗: $e');
    }

    return events;
  }

  /// 判斷是否為重要的行星相位
  bool _isImportantPlanetAspect(String planet1, String planet2) {
    final importantPlanets = ['太陽', '月亮', '水星', '金星', '火星', '木星', '土星'];
    return importantPlanets.contains(planet1) && importantPlanets.contains(planet2);
  }

  /// 判斷是否為重要行星
  bool _isImportantPlanet(String planetName) {
    final importantPlanets = ['太陽', '月亮', '水星', '金星', '火星', '木星', '土星', '天王星', '海王星', '冥王星'];
    return importantPlanets.contains(planetName);
  }

  /// 獲取相位顏色
  Color _getAspectColor(String aspectType) {
    switch (aspectType) {
      case '合相':
        return Colors.red;
      case '對分相':
        return Colors.orange;
      case '四分相':
        return Colors.amber;
      case '三分相':
        return Colors.green;
      case '六分相':
        return Colors.blue;
      default:
        return Colors.grey;
    }
  }

  /// 獲取相位重要度
  int _getAspectImportance(String aspectType) {
    switch (aspectType) {
      case '合相':
        return 5;
      case '對分相':
        return 4;
      case '四分相':
        return 3;
      case '三分相':
        return 2;
      case '六分相':
        return 1;
      default:
        return 1;
    }
  }

  /// 獲取行星重要度
  int _getPlanetImportance(String planetName) {
    switch (planetName) {
      case '太陽':
      case '月亮':
        return 5;
      case '水星':
      case '金星':
      case '火星':
        return 4;
      case '木星':
      case '土星':
        return 3;
      case '天王星':
      case '海王星':
      case '冥王星':
        return 2;
      default:
        return 1;
    }
  }

  /// 獲取日月蝕事件
  Future<List<AstroEvent>> _getEclipseEvents(
    int year,
    int month,
    double latitude,
    double longitude,
  ) async {
    final events = <AstroEvent>[];

    try {
      // 計算該月的日月蝕事件
      final startDate = DateTime(year, month, 1);
      final endDate = DateTime(year, month + 1, 0);

      // 獲取日蝕事件
      final solarEclipses = await _getSolarEclipses(startDate, endDate, latitude, longitude);
      events.addAll(solarEclipses);

      // 獲取月蝕事件
      final lunarEclipses = await _getLunarEclipses(startDate, endDate, latitude, longitude);
      events.addAll(lunarEclipses);

      logger.d('計算 $year年$month月 日月蝕事件: ${events.length} 個');

      // 調試信息：顯示找到的日月蝕事件
      for (final event in events) {
        logger.d('找到日月蝕事件: ${event.title} - ${event.dateTime}');
      }
    } catch (e) {
      logger.e('計算日月蝕事件失敗: $e');
    }

    return events;
  }

  /// 獲取日蝕事件
  Future<List<AstroEvent>> _getSolarEclipses(
    DateTime startDate,
    DateTime endDate,
    double latitude,
    double longitude,
  ) async {
    final events = <AstroEvent>[];

    try {
      // 精確的日蝕計算：使用已知的日蝕日期和時間（UTC）
      final knownSolarEclipses = [
        {
          'date': DateTime.utc(2025, 3, 29, 10, 47, 27), // 2025年3月29日日偏蝕，最大蝕分時間
          'type': EclipseType.solarPartial,
          'magnitude': 0.944, // 最大蝕分
          'description': '北大西洋、歐洲、亞洲北部、北非、北美洲大部可見'
        },
        {
          'date': DateTime.utc(2025, 9, 21, 19, 42, 54), // 2025年9月21日日偏蝕
          'type': EclipseType.solarPartial,
          'magnitude': 0.855,
          'description': '南太平洋、紐西蘭、南極洲可見'
        },
        {
          'date': DateTime.utc(2026, 2, 17, 12, 13, 6), // 2026年2月17日日環蝕
          'type': EclipseType.solarAnnular,
          'magnitude': 0.963,
          'description': '南極洲可見環蝕，南美洲南部、南非可見偏蝕'
        },
        {
          'date': DateTime.utc(2026, 8, 12, 17, 47, 6), // 2026年8月12日日全蝕
          'type': EclipseType.solarTotal,
          'magnitude': 1.039,
          'description': '格陵蘭、冰島、西班牙、俄羅斯可見全蝕'
        },
        {
          'date': DateTime.utc(2027, 2, 6, 16, 57, 18), // 2027年2月6日日環蝕
          'type': EclipseType.solarAnnular,
          'magnitude': 0.928,
          'description': '南美洲、南大西洋、非洲可見'
        },
        {
          'date': DateTime.utc(2027, 8, 2, 10, 7, 50), // 2027年8月2日日全蝕
          'type': EclipseType.solarTotal,
          'magnitude': 1.079,
          'description': '摩洛哥、西班牙、阿爾及利亞、埃及可見全蝕'
        },
        {
          'date': DateTime.utc(2028, 1, 26, 15, 8, 59), // 2028年1月26日日環蝕
          'type': EclipseType.solarAnnular,
          'magnitude': 0.921,
          'description': '厄瓜多、秘魯、巴西、葡萄牙、西班牙可見'
        },
        {
          'date': DateTime.utc(2028, 7, 22, 2, 56, 40), // 2028年7月22日日全蝕
          'type': EclipseType.solarTotal,
          'magnitude': 1.056,
          'description': '澳洲、紐西蘭可見全蝕'
        },
      ];

      // 篩選在指定日期範圍內的日蝕
      for (final eclipse in knownSolarEclipses) {
        final eclipseDate = eclipse['date'] as DateTime;
        final eclipseType = eclipse['type'] as EclipseType;
        final magnitude = eclipse['magnitude'] as double;
        final description = eclipse['description'] as String;

        if (eclipseDate.isAfter(startDate.subtract(const Duration(days: 1))) &&
            eclipseDate.isBefore(endDate.add(const Duration(days: 1)))) {

          // 轉換為本地時間
          final localDateTime = eclipseDate.toLocal();

          events.add(AstroEvent(
            id: _uuid.v4(),
            title: AstroEvent.getEclipseDisplayName(eclipseType),
            description: '$description\n蝕甚時間：${_formatEclipseTime(localDateTime)}\n蝕分：${magnitude.toStringAsFixed(3)}',
            dateTime: localDateTime,
            type: AstroEventType.eclipse,
            color: AstroEvent.getEclipseColor(eclipseType),
            icon: AstroEvent.getEclipseIcon(eclipseType),
            importance: _getEclipseImportance(eclipseType),
            additionalData: {
              'eclipseType': eclipseType,
              'isVisible': _isEclipseVisibleByRegion(description, latitude, longitude),
              'magnitude': magnitude,
              'utcTime': eclipseDate,
              'localTime': localDateTime,
              'region': description,
            },
          ));

          logger.d('添加日蝕事件: ${AstroEvent.getEclipseDisplayName(eclipseType)} - ${_formatEclipseTime(localDateTime)} (UTC: $eclipseDate)');
        }
      }
    } catch (e) {
      logger.e('計算日蝕事件失敗: $e');
    }

    return events;
  }

  /// 獲取月蝕事件
  Future<List<AstroEvent>> _getLunarEclipses(
    DateTime startDate,
    DateTime endDate,
    double latitude,
    double longitude,
  ) async {
    final events = <AstroEvent>[];

    try {
      // 精確的月蝕計算：使用已知的月蝕日期和時間（UTC）
      final knownLunarEclipses = [
        {
          'date': DateTime.utc(2025, 3, 14, 6, 58, 27), // 2025年3月14日月全蝕，蝕甚時間
          'type': EclipseType.lunarTotal,
          'magnitude': 1.178, // 蝕分
          'description': '太平洋、美洲、西歐、西非可見'
        },
        {
          'date': DateTime.utc(2025, 9, 7, 18, 11, 43), // 2025年9月7日月全蝕
          'type': EclipseType.lunarTotal,
          'magnitude': 1.362,
          'description': '歐洲、非洲、亞洲、澳洲可見'
        },
        {
          'date': DateTime.utc(2026, 3, 3, 11, 33, 26), // 2026年3月3日月全蝕
          'type': EclipseType.lunarTotal,
          'magnitude': 1.150,
          'description': '東亞、澳洲、太平洋、美洲可見'
        },
        {
          'date': DateTime.utc(2026, 8, 28, 4, 12, 58), // 2026年8月28日月偏蝕
          'type': EclipseType.lunarPartial,
          'magnitude': 0.928,
          'description': '東歐、亞洲、澳洲、太平洋可見'
        },
        {
          'date': DateTime.utc(2027, 2, 20, 23, 11, 30), // 2027年2月20日月偏蝕
          'type': EclipseType.lunarPartial,
          'magnitude': 0.923,
          'description': '美洲、歐洲、非洲、亞洲西部可見'
        },
        {
          'date': DateTime.utc(2027, 7, 18, 12, 2, 7), // 2027年7月18日月偏蝕
          'type': EclipseType.lunarPartial,
          'magnitude': 0.861,
          'description': '南美洲、歐洲、非洲、亞洲、澳洲可見'
        },
        {
          'date': DateTime.utc(2028, 1, 12, 4, 12, 24), // 2028年1月12日月偏蝕
          'type': EclipseType.lunarPartial,
          'magnitude': 0.066,
          'description': '歐洲、非洲、亞洲、澳洲可見'
        },
        {
          'date': DateTime.utc(2028, 7, 6, 18, 20, 35), // 2028年7月6日月偏蝕
          'type': EclipseType.lunarPartial,
          'magnitude': 0.389,
          'description': '南美洲、歐洲、非洲、亞洲西部可見'
        },
      ];

      // 篩選在指定日期範圍內的月蝕
      for (final eclipse in knownLunarEclipses) {
        final eclipseDate = eclipse['date'] as DateTime;
        final eclipseType = eclipse['type'] as EclipseType;
        final magnitude = eclipse['magnitude'] as double;
        final description = eclipse['description'] as String;

        if (eclipseDate.isAfter(startDate.subtract(const Duration(days: 1))) &&
            eclipseDate.isBefore(endDate.add(const Duration(days: 1)))) {

          // 轉換為本地時間
          final localDateTime = eclipseDate.toLocal();

          events.add(AstroEvent(
            id: _uuid.v4(),
            title: AstroEvent.getEclipseDisplayName(eclipseType),
            description: '$description\n蝕甚時間：${_formatEclipseTime(localDateTime)}\n蝕分：${magnitude.toStringAsFixed(3)}',
            dateTime: localDateTime,
            type: AstroEventType.eclipse,
            color: AstroEvent.getEclipseColor(eclipseType),
            icon: AstroEvent.getEclipseIcon(eclipseType),
            importance: _getEclipseImportance(eclipseType),
            additionalData: {
              'eclipseType': eclipseType,
              'isVisible': _isEclipseVisibleByRegion(description, latitude, longitude),
              'magnitude': magnitude,
              'utcTime': eclipseDate,
              'localTime': localDateTime,
              'region': description,
            },
          ));

          logger.d('添加月蝕事件: ${AstroEvent.getEclipseDisplayName(eclipseType)} - ${_formatEclipseTime(localDateTime)} (UTC: $eclipseDate)');
        }
      }
    } catch (e) {
      logger.e('計算月蝕事件失敗: $e');
    }

    return events;
  }

  /// 查找下一個日蝕（模擬實現）
  /// 注意：這是一個簡化的實現，實際應該使用 Swiss Ephemeris 的日蝕計算 API
  Future<double> _findNextSolarEclipse(double startJd, double latitude, double longitude) async {
    // 這裡使用簡化的算法來模擬日蝕計算
    // 實際實現應該使用 Swiss Ephemeris 的 swe_sol_eclipse_when_loc 或 swe_sol_eclipse_when_glob

    // 日蝕大約每6個月發生一次，但不是每次都在同一地點可見
    // 這裡使用一個簡化的週期來模擬
    const double solarEclipseCycle = 177.0; // 大約6個月的天數

    // 基於已知的日蝕日期來計算（這裡使用一些實際的日蝕日期作為參考）
    final knownEclipses = [
      2459945.0, // 2022年10月25日日偏蝕
      2460064.0, // 2023年4月20日日混合蝕
      2460201.0, // 2023年10月14日日環蝕
      2460419.0, // 2024年4月8日日全蝕
      2460564.0, // 2024年10月2日日環蝕
      2460748.0, // 2025年3月29日日偏蝕
      2460931.0, // 2025年9月21日日偏蝕
      2461113.0, // 2026年2月17日日環蝕
      2461296.0, // 2026年8月12日日全蝕
      2461479.0, // 2027年2月6日日環蝕
      2461662.0, // 2027年8月2日日全蝕
      2461845.0, // 2028年1月26日日環蝕
      2462028.0, // 2028年7月22日日全蝕
    ];

    // 找到最接近的未來日蝕
    for (final eclipseJd in knownEclipses) {
      if (eclipseJd > startJd) {
        return eclipseJd;
      }
    }

    // 如果沒有找到已知的日蝕，返回-1表示沒有找到
    return -1.0;
  }

  /// 查找下一個月蝕（模擬實現）
  Future<double> _findNextLunarEclipse(double startJd, double latitude, double longitude) async {
    // 月蝕大約每6個月發生一次
    const double lunarEclipseCycle = 177.0;

    // 基於已知的月蝕日期來計算
    final knownEclipses = [
      2459889.0, // 2022年11月8日月全蝕
      2460072.0, // 2023年5月5日月偏蝕
      2460235.0, // 2023年10月28日月偏蝕
      2460388.0, // 2024年3月25日月偏蝕
      2460543.0, // 2024年9月18日月偏蝕
      2460726.0, // 2025年3月14日月全蝕
      2460909.0, // 2025年9月7日月全蝕
      2461091.0, // 2026年3月3日月全蝕
      2461274.0, // 2026年8月28日月偏蝕
      2461457.0, // 2027年2月20日月偏蝕
      2461640.0, // 2027年7月18日月偏蝕
      2461823.0, // 2028年1月12日月偏蝕
      2462006.0, // 2028年7月6日月偏蝕
    ];

    // 找到最接近的未來月蝕
    for (final eclipseJd in knownEclipses) {
      if (eclipseJd > startJd) {
        return eclipseJd;
      }
    }

    return -1.0;
  }

  /// 判斷日蝕類型
  EclipseType _determineSolarEclipseType(double eclipseJd, double latitude, double longitude) {
    // 這裡使用簡化的邏輯來判斷日蝕類型
    // 實際實現應該使用 Swiss Ephemeris 的 swe_sol_eclipse_how 函數

    // 基於儒略日來模擬不同類型的日蝕
    final dayOfYear = (eclipseJd % 365.25).toInt();

    if (dayOfYear < 90) {
      return EclipseType.solarTotal;
    } else if (dayOfYear < 180) {
      return EclipseType.solarAnnular;
    } else if (dayOfYear < 270) {
      return EclipseType.solarPartial;
    } else {
      return EclipseType.solarHybrid;
    }
  }

  /// 判斷月蝕類型
  EclipseType _determineLunarEclipseType(double eclipseJd, double latitude, double longitude) {
    // 簡化的月蝕類型判斷
    final dayOfYear = (eclipseJd % 365.25).toInt();

    if (dayOfYear < 120) {
      return EclipseType.lunarTotal;
    } else if (dayOfYear < 240) {
      return EclipseType.lunarPartial;
    } else {
      return EclipseType.lunarPenumbral;
    }
  }

  /// 判斷蝕相是否在指定地點可見
  bool _isEclipseVisible(double eclipseJd, double latitude, double longitude) {
    // 簡化的可見性判斷
    // 實際實現應該考慮地球的幾何形狀和蝕相的路徑

    // 這裡假設大部分蝕相在某些地區都是可見的
    return true; // 簡化處理，實際應該根據地理位置和蝕相類型來判斷
  }

  /// 獲取蝕相的蝕分（蝕相的程度）
  double _getEclipseMagnitude(double eclipseJd, double latitude, double longitude) {
    // 簡化的蝕分計算
    // 實際實現應該使用 Swiss Ephemeris 的相關函數

    // 返回一個0.1到1.0之間的隨機值作為蝕分
    final random = (eclipseJd * 1000) % 100;
    return 0.1 + (random / 100) * 0.9;
  }

  /// 獲取蝕相的重要度
  int _getEclipseImportance(EclipseType eclipseType) {
    switch (eclipseType) {
      case EclipseType.solarTotal:
        return 5; // 日全蝕最重要
      case EclipseType.lunarTotal:
        return 4; // 月全蝕次之
      case EclipseType.solarAnnular:
      case EclipseType.solarHybrid:
        return 3; // 日環蝕和混合蝕
      case EclipseType.solarPartial:
      case EclipseType.lunarPartial:
        return 2; // 偏蝕
      case EclipseType.lunarPenumbral:
        return 1; // 半影月蝕最低
    }
  }

  /// 格式化日月蝕時間顯示
  String _formatEclipseTime(DateTime dateTime) {
    return '${dateTime.year}年${dateTime.month}月${dateTime.day}日 '
           '${dateTime.hour.toString().padLeft(2, '0')}:'
           '${dateTime.minute.toString().padLeft(2, '0')}:'
           '${dateTime.second.toString().padLeft(2, '0')}';
  }

  /// 根據地區描述判斷日月蝕可見性
  bool _isEclipseVisibleByRegion(String regionDescription, double latitude, double longitude) {
    // 簡化的可見性判斷：根據地區描述和用戶位置
    final description = regionDescription.toLowerCase();

    // 台灣地區的判斷
    if (latitude >= 22.0 && latitude <= 26.0 && longitude >= 120.0 && longitude <= 122.0) {
      // 台灣位置
      if (description.contains('亞洲') || description.contains('東亞') ||
          description.contains('太平洋') || description.contains('澳洲')) {
        return true;
      }
    }

    // 其他地區的簡化判斷
    if (description.contains('全球') || description.contains('worldwide')) {
      return true;
    }

    // 默認返回部分可見
    return false;
  }
}
