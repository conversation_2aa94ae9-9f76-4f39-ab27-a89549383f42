import 'dart:math';
import 'package:flutter/material.dart';
import 'package:sweph/sweph.dart';
import 'package:uuid/uuid.dart';

import '../models/astro_event.dart';
import '../models/birth_data.dart';
import '../models/planet_position.dart';
import '../ui/AppTheme.dart';
import '../utils/LoggerUtils.dart';
import '../utils/julian_date_utils.dart';
import 'astrology_service.dart';
import 'equinox_solstice_service.dart';

/// 星象日曆服務
/// 負責計算和提供日曆相關的星象事件
class AstroCalendarService {
  static final AstroCalendarService _instance = AstroCalendarService._internal();
  factory AstroCalendarService() => _instance;
  AstroCalendarService._internal();

  final EquinoxSolsticeService _equinoxService = EquinoxSolsticeService();
  final AstrologyService _astrologyService = AstrologyService();
  final Uuid _uuid = const Uuid();

  /// 獲取指定月份的所有星象事件
  Future<List<AstroEvent>> getMonthlyEvents(
    int year,
    int month, {
    double latitude = 25.0,
    double longitude = 121.0,
    BirthData? natalPerson,
  }) async {
    final events = <AstroEvent>[];

    try {
      // 獲取月相事件
      final moonPhaseEvents = await _getMoonPhaseEvents(year, month, latitude, longitude);
      events.addAll(moonPhaseEvents);

      // 獲取節氣事件
      final seasonEvents = await _getSeasonEvents(year, month, latitude, longitude);
      events.addAll(seasonEvents);

      // 獲取行星相位事件
      final aspectEvents = await _getPlanetAspectEvents(year, month, latitude, longitude);
      events.addAll(aspectEvents);

      // 獲取行星換座事件
      final signChangeEvents = await _getPlanetSignChangeEvents(year, month, latitude, longitude);
      events.addAll(signChangeEvents);

      // 獲取日月蝕事件
      final eclipseEvents = await _getEclipseEvents(year, month, latitude, longitude);
      events.addAll(eclipseEvents);

      // 按日期排序
      events.sort((a, b) => a.dateTime.compareTo(b.dateTime));

      logger.i('獲取 $year年$month月 星象事件: ${events.length} 個');
    } catch (e) {
      logger.e('獲取月度星象事件失敗: $e');
    }

    return events;
  }

  /// 獲取指定日期的星象事件
  Future<List<AstroEvent>> getDailyEvents(
    DateTime date, {
    double latitude = 25.0,
    double longitude = 121.0,
    BirthData? natalPerson,
  }) async {
    final events = <AstroEvent>[];

    try {
      // 獲取當日的所有事件
      final monthlyEvents = await getMonthlyEvents(
        date.year,
        date.month,
        latitude: latitude,
        longitude: longitude,
        natalPerson: natalPerson,
      );

      // 篩選當日事件
      final dailyEvents = monthlyEvents.where((event) {
        return event.dateTime.year == date.year &&
            event.dateTime.month == date.month &&
            event.dateTime.day == date.day;
      }).toList();

      events.addAll(dailyEvents);

      logger.d('獲取 ${date.toString().split(' ')[0]} 星象事件: ${events.length} 個');
    } catch (e) {
      logger.e('獲取日度星象事件失敗: $e');
    }

    return events;
  }

  /// 獲取月相事件
  Future<List<AstroEvent>> _getMoonPhaseEvents(
    int year,
    int month,
    double latitude,
    double longitude,
  ) async {
    final events = <AstroEvent>[];

    try {
      // 計算該月的月相
      final startDate = DateTime(year, month, 1);
      final endDate = DateTime(year, month + 1, 0);

      // 簡化的月相計算 - 每7.4天一個月相
      final moonCycle = 29.53; // 月相週期
      final phaseDuration = moonCycle / 4; // 每個主要月相間隔

      // 從月初開始計算
      for (int day = 1; day <= endDate.day; day += 7) {
        final date = DateTime(year, month, day);
        final moonPhase = _calculateMoonPhase(date);

        if (moonPhase != null) {
          events.add(AstroEvent(
            id: _uuid.v4(),
            title: AstroEvent.getMoonPhaseDisplayName(moonPhase),
            description: '月相變化：${AstroEvent.getMoonPhaseDisplayName(moonPhase)}',
            dateTime: date,
            type: AstroEventType.moonPhase,
            color: Colors.indigo,
            icon: AstroEvent.getMoonPhaseIcon(moonPhase),
            importance: moonPhase == MoonPhaseType.newMoon || moonPhase == MoonPhaseType.fullMoon ? 4 : 2,
            additionalData: {'moonPhase': moonPhase},
          ));
        }
      }
    } catch (e) {
      logger.e('計算月相事件失敗: $e');
    }

    return events;
  }

  /// 獲取節氣事件
  Future<List<AstroEvent>> _getSeasonEvents(
    int year,
    int month,
    double latitude,
    double longitude,
  ) async {
    final events = <AstroEvent>[];

    try {
      // 獲取該年的節氣數據
      final seasons = await _equinoxService.calculateSeasonTimes(
        year,
        latitude: latitude,
        longitude: longitude,
      );

      // 篩選該月的節氣
      for (final season in seasons) {
        if (season.dateTime.month == month) {
          events.add(AstroEvent(
            id: _uuid.v4(),
            title: season.seasonType.displayName,
            description: '節氣變化：${season.seasonType.displayName}',
            dateTime: season.dateTime,
            type: AstroEventType.seasonChange,
            color: _getSeasonColor(season.seasonType),
            icon: _getSeasonIcon(season.seasonType),
            importance: 5,
            additionalData: {'seasonType': season.seasonType},
          ));
        }
      }
    } catch (e) {
      logger.e('計算節氣事件失敗: $e');
    }

    return events;
  }

  /// 簡化的月相計算
  MoonPhaseType? _calculateMoonPhase(DateTime date) {
    // 這是一個簡化的月相計算，實際應用中應該使用更精確的天文計算
    final dayOfMonth = date.day;

    if (dayOfMonth <= 2) return MoonPhaseType.newMoon;
    if (dayOfMonth <= 7) return MoonPhaseType.firstQuarter;
    if (dayOfMonth <= 16) return MoonPhaseType.fullMoon;
    if (dayOfMonth <= 23) return MoonPhaseType.lastQuarter;
    if (dayOfMonth <= 30) return MoonPhaseType.newMoon;

    return null;
  }

  /// 獲取節氣顏色
  Color _getSeasonColor(SeasonType seasonType) {
    switch (seasonType) {
      case SeasonType.springEquinox:
        return Colors.green;
      case SeasonType.summerSolstice:
        return AppColors.solarAmber;
      case SeasonType.autumnEquinox:
        return Colors.orange;
      case SeasonType.winterSolstice:
        return Colors.blue;
    }
  }

  /// 獲取節氣圖標
  IconData _getSeasonIcon(SeasonType seasonType) {
    switch (seasonType) {
      case SeasonType.springEquinox:
        return Icons.eco;
      case SeasonType.summerSolstice:
        return Icons.wb_sunny;
      case SeasonType.autumnEquinox:
        return Icons.park;
      case SeasonType.winterSolstice:
        return Icons.ac_unit;
    }
  }

  /// 獲取行星相位事件
  Future<List<AstroEvent>> _getPlanetAspectEvents(
    int year,
    int month,
    double latitude,
    double longitude,
  ) async {
    final events = <AstroEvent>[];

    try {
      // 計算該月重要的行星相位
      final startDate = DateTime(year, month, 1);
      final endDate = DateTime(year, month + 1, 0);

      // 每5天檢查一次重要相位
      for (int day = 1; day <= endDate.day; day += 5) {
        final date = DateTime(year, month, day, 12); // 使用中午時間

        // 計算當日行星位置
        final planets = await _astrologyService.calculatePlanetPositions(
          date,
          latitude: latitude,
          longitude: longitude,
        );

        // 計算相位
        final aspects = _astrologyService.calculateAspects(planets);

        // 篩選重要相位（合相、對分、四分）
        final importantAspects = aspects.where((aspect) {
          return aspect.aspect == '合相' ||
              aspect.aspect == '對分相' ||
              aspect.aspect == '四分相';
        }).toList();

        // 添加重要相位事件
        for (final aspect in importantAspects) {
          // 只添加外行星的重要相位
          if (_isImportantPlanetAspect(aspect.planet1.name, aspect.planet2.name)) {
            events.add(AstroEvent(
              id: _uuid.v4(),
              title: '${aspect.planet1.name}${aspect.aspect}${aspect.planet2.name}',
              description: '${aspect.planet1.name}與${aspect.planet2.name}形成${aspect.aspect}相位',
              dateTime: date,
              type: AstroEventType.planetAspect,
              color: _getAspectColor(aspect.aspect),
              icon: Icons.timeline,
              importance: _getAspectImportance(aspect.aspect),
              additionalData: {
                'aspect': aspect,
                'planet1': aspect.planet1.name,
                'planet2': aspect.planet2.name,
                'aspectType': aspect.aspect,
              },
            ));
          }
        }
      }
    } catch (e) {
      logger.e('計算行星相位事件失敗: $e');
    }

    return events;
  }

  /// 獲取行星換座事件
  Future<List<AstroEvent>> _getPlanetSignChangeEvents(
    int year,
    int month,
    double latitude,
    double longitude,
  ) async {
    final events = <AstroEvent>[];

    try {
      // 計算該月的行星換座
      final startDate = DateTime(year, month, 1);
      final endDate = DateTime(year, month + 1, 0);

      // 每天檢查行星位置變化
      DateTime? previousDate;
      List<PlanetPosition>? previousPlanets;

      for (int day = 1; day <= endDate.day; day++) {
        final date = DateTime(year, month, day, 12);

        final planets = await _astrologyService.calculatePlanetPositions(
          date,
          latitude: latitude,
          longitude: longitude,
        );

        if (previousPlanets != null && previousDate != null) {
          // 檢查行星是否換座
          for (int i = 0; i < planets.length && i < previousPlanets.length; i++) {
            final currentPlanet = planets[i];
            final previousPlanet = previousPlanets[i];

            if (currentPlanet.name == previousPlanet.name &&
                currentPlanet.sign != previousPlanet.sign &&
                _isImportantPlanet(currentPlanet.name)) {
              events.add(AstroEvent(
                id: _uuid.v4(),
                title: '${currentPlanet.name}進入${currentPlanet.sign}',
                description: '${currentPlanet.name}從${previousPlanet.sign}進入${currentPlanet.sign}',
                dateTime: date,
                type: AstroEventType.planetSignChange,
                color: currentPlanet.color,
                icon: Icons.swap_horiz,
                importance: _getPlanetImportance(currentPlanet.name),
                additionalData: {
                  'planet': currentPlanet.name,
                  'fromSign': previousPlanet.sign,
                  'toSign': currentPlanet.sign,
                },
              ));
            }
          }
        }

        previousDate = date;
        previousPlanets = planets;
      }
    } catch (e) {
      logger.e('計算行星換座事件失敗: $e');
    }

    return events;
  }

  /// 判斷是否為重要的行星相位
  bool _isImportantPlanetAspect(String planet1, String planet2) {
    final importantPlanets = ['太陽', '月亮', '水星', '金星', '火星', '木星', '土星'];
    return importantPlanets.contains(planet1) && importantPlanets.contains(planet2);
  }

  /// 判斷是否為重要行星
  bool _isImportantPlanet(String planetName) {
    final importantPlanets = ['太陽', '月亮', '水星', '金星', '火星', '木星', '土星', '天王星', '海王星', '冥王星'];
    return importantPlanets.contains(planetName);
  }

  /// 獲取相位顏色
  Color _getAspectColor(String aspectType) {
    switch (aspectType) {
      case '合相':
        return Colors.red;
      case '對分相':
        return Colors.orange;
      case '四分相':
        return Colors.amber;
      case '三分相':
        return Colors.green;
      case '六分相':
        return Colors.blue;
      default:
        return Colors.grey;
    }
  }

  /// 獲取相位重要度
  int _getAspectImportance(String aspectType) {
    switch (aspectType) {
      case '合相':
        return 5;
      case '對分相':
        return 4;
      case '四分相':
        return 3;
      case '三分相':
        return 2;
      case '六分相':
        return 1;
      default:
        return 1;
    }
  }

  /// 獲取行星重要度
  int _getPlanetImportance(String planetName) {
    switch (planetName) {
      case '太陽':
      case '月亮':
        return 5;
      case '水星':
      case '金星':
      case '火星':
        return 4;
      case '木星':
      case '土星':
        return 3;
      case '天王星':
      case '海王星':
      case '冥王星':
        return 2;
      default:
        return 1;
    }
  }

  /// 獲取日月蝕事件
  Future<List<AstroEvent>> _getEclipseEvents(
    int year,
    int month,
    double latitude,
    double longitude,
  ) async {
    final events = <AstroEvent>[];

    try {
      // 計算該月的日月蝕事件
      final startDate = DateTime(year, month, 1);
      final endDate = DateTime(year, month + 1, 0);

      // 獲取日蝕事件
      final solarEclipses = await _getSolarEclipses(startDate, endDate, latitude, longitude);
      events.addAll(solarEclipses);

      // 獲取月蝕事件
      final lunarEclipses = await _getLunarEclipses(startDate, endDate, latitude, longitude);
      events.addAll(lunarEclipses);

      logger.d('計算 $year年$month月 日月蝕事件: ${events.length} 個');

      // 調試信息：顯示找到的日月蝕事件
      for (final event in events) {
        logger.d('找到日月蝕事件: ${event.title} - ${event.dateTime}');
      }
    } catch (e) {
      logger.e('計算日月蝕事件失敗: $e');
    }

    return events;
  }

  /// 獲取日蝕事件 - 使用Swiss Ephemeris動態計算
  Future<List<AstroEvent>> _getSolarEclipses(
    DateTime startDate,
    DateTime endDate,
    double latitude,
    double longitude,
  ) async {
    final events = <AstroEvent>[];

    try {
      // 使用Swiss Ephemeris計算日蝕
      final eclipses = await _calculateSolarEclipsesWithSwissEph(
        startDate,
        endDate,
        latitude,
        longitude
      );

      for (final eclipse in eclipses) {
        events.add(AstroEvent(
          id: _uuid.v4(),
          title: AstroEvent.getEclipseDisplayName(eclipse['type'] as EclipseType),
          description: '${eclipse['description']}\n蝕甚時間：${_formatEclipseTime(eclipse['dateTime'] as DateTime)}\n蝕分：${(eclipse['magnitude'] as double).toStringAsFixed(3)}',
          dateTime: eclipse['dateTime'] as DateTime,
          type: AstroEventType.eclipse,
          color: AstroEvent.getEclipseColor(eclipse['type'] as EclipseType),
          icon: AstroEvent.getEclipseIcon(eclipse['type'] as EclipseType),
          importance: _getEclipseImportance(eclipse['type'] as EclipseType),
          additionalData: {
            'eclipseType': eclipse['type'],
            'isVisible': eclipse['isVisible'],
            'magnitude': eclipse['magnitude'],
            'utcTime': eclipse['utcTime'],
            'localTime': eclipse['dateTime'],
            'region': eclipse['description'],
            'duration': eclipse['duration'],
          },
        ));

        logger.d('添加日蝕事件: ${AstroEvent.getEclipseDisplayName(eclipse['type'] as EclipseType)} - ${_formatEclipseTime(eclipse['dateTime'] as DateTime)}');
      }
    } catch (e) {
      logger.e('計算日蝕事件失敗: $e');
    }

    return events;
  }

  /// 獲取月蝕事件 - 使用Swiss Ephemeris動態計算
  Future<List<AstroEvent>> _getLunarEclipses(
    DateTime startDate,
    DateTime endDate,
    double latitude,
    double longitude,
  ) async {
    final events = <AstroEvent>[];

    try {
      // 使用Swiss Ephemeris計算月蝕
      final eclipses = await _calculateLunarEclipsesWithSwissEph(
        startDate,
        endDate,
        latitude,
        longitude
      );

      for (final eclipse in eclipses) {
        events.add(AstroEvent(
          id: _uuid.v4(),
          title: AstroEvent.getEclipseDisplayName(eclipse['type'] as EclipseType),
          description: '${eclipse['description']}\n蝕甚時間：${_formatEclipseTime(eclipse['dateTime'] as DateTime)}\n蝕分：${(eclipse['magnitude'] as double).toStringAsFixed(3)}',
          dateTime: eclipse['dateTime'] as DateTime,
          type: AstroEventType.eclipse,
          color: AstroEvent.getEclipseColor(eclipse['type'] as EclipseType),
          icon: AstroEvent.getEclipseIcon(eclipse['type'] as EclipseType),
          importance: _getEclipseImportance(eclipse['type'] as EclipseType),
          additionalData: {
            'eclipseType': eclipse['type'],
            'isVisible': eclipse['isVisible'],
            'magnitude': eclipse['magnitude'],
            'utcTime': eclipse['utcTime'],
            'localTime': eclipse['dateTime'],
            'region': eclipse['description'],
            'duration': eclipse['duration'],
          },
        ));

        logger.d('添加月蝕事件: ${AstroEvent.getEclipseDisplayName(eclipse['type'] as EclipseType)} - ${_formatEclipseTime(eclipse['dateTime'] as DateTime)}');
      }
    } catch (e) {
      logger.e('計算月蝕事件失敗: $e');
    }

    return events;
  }

  /// 使用Swiss Ephemeris計算日蝕事件
  Future<List<Map<String, dynamic>>> _calculateSolarEclipsesWithSwissEph(
    DateTime startDate,
    DateTime endDate,
    double latitude,
    double longitude,
  ) async {
    final eclipses = <Map<String, dynamic>>[];

    try {
      // 轉換為儒略日
      final startJd = await JulianDateUtils.dateTimeToJulianDay(startDate, latitude, longitude);
      final endJd = await JulianDateUtils.dateTimeToJulianDay(endDate, latitude, longitude);

      // 搜索日蝕事件
      double currentJd = startJd;

      while (currentJd < endJd) {
        // 查找下一個日蝕
        final eclipseJd = await _findNextSolarEclipseSwissEph(currentJd, latitude, longitude);

        if (eclipseJd > 0 && eclipseJd <= endJd) {
          // 獲取日蝕詳細信息
          final eclipseInfo = await _getSolarEclipseDetails(eclipseJd, latitude, longitude);

          if (eclipseInfo != null) {
            eclipses.add(eclipseInfo);
          }

          // 移動到下一個搜索點（至少6個月後）
          currentJd = eclipseJd + 180; // 6個月後
        } else {
          // 如果沒有找到更多日蝕，退出循環
          break;
        }
      }
    } catch (e) {
      logger.e('Swiss Ephemeris日蝕計算失敗: $e');
    }

    return eclipses;
  }

  /// 使用Swiss Ephemeris計算月蝕事件
  Future<List<Map<String, dynamic>>> _calculateLunarEclipsesWithSwissEph(
    DateTime startDate,
    DateTime endDate,
    double latitude,
    double longitude,
  ) async {
    final eclipses = <Map<String, dynamic>>[];

    try {
      // 轉換為儒略日
      final startJd = await JulianDateUtils.dateTimeToJulianDay(startDate, latitude, longitude);
      final endJd = await JulianDateUtils.dateTimeToJulianDay(endDate, latitude, longitude);

      // 搜索月蝕事件
      double currentJd = startJd;

      while (currentJd < endJd) {
        // 查找下一個月蝕
        final eclipseJd = await _findNextLunarEclipseSwissEph(currentJd, latitude, longitude);

        if (eclipseJd > 0 && eclipseJd <= endJd) {
          // 獲取月蝕詳細信息
          final eclipseInfo = await _getLunarEclipseDetails(eclipseJd, latitude, longitude);

          if (eclipseInfo != null) {
            eclipses.add(eclipseInfo);
          }

          // 移動到下一個搜索點（至少6個月後）
          currentJd = eclipseJd + 180; // 6個月後
        } else {
          // 如果沒有找到更多月蝕，退出循環
          break;
        }
      }
    } catch (e) {
      logger.e('Swiss Ephemeris月蝕計算失敗: $e');
    }

    return eclipses;
  }

  /// 查找下一個日蝕（使用真正的Swiss Ephemeris函數）
  Future<double> _findNextSolarEclipseSwissEph(double startJd, double latitude, double longitude) async {
    try {
      logger.d('使用Swiss Ephemeris swe_sol_eclipse_when_glob搜索日蝕，起始儒略日: $startJd');

      // 使用真正的Swiss Ephemeris函數搜索全球日蝕
      final eclipseInfo = Sweph.swe_sol_eclipse_when_glob(
        startJd,
        SwephFlag.SEFLG_SWIEPH, // 使用Swiss Ephemeris
        EclipseFlag.SE_ECL_ALLTYPES_SOLAR, // 搜索所有類型的日蝕
        false, // 向前搜索
      );

      if (eclipseInfo.times!.isNotEmpty && eclipseInfo.times![0] > 0) {
        final eclipseJd = eclipseInfo.times![0]; // 蝕甚時間
        logger.d('Swiss Ephemeris找到日蝕事件，儒略日: $eclipseJd, 類型: ${eclipseInfo.eclipseType}');

        // 檢查這個日蝕在指定地點是否可見
        final isVisible = await _checkSolarEclipseVisibility(eclipseJd, latitude, longitude);

        if (isVisible) {
          return eclipseJd;
        } else {
          // 如果在當前位置不可見，繼續搜索下一個
          return await _findNextSolarEclipseSwissEph(eclipseJd + 1, latitude, longitude);
        }
      }

      logger.d('Swiss Ephemeris未找到日蝕事件');
      return -1; // 沒有找到日蝕
    } catch (e) {
      logger.e('Swiss Ephemeris查找日蝕失敗: $e');
      // 如果Swiss Ephemeris調用失敗，回退到我們的實現
      return await _findNextSolarEclipseBackup(startJd, latitude, longitude);
    }
  }

  /// 查找下一個月蝕（使用真正的Swiss Ephemeris函數）
  Future<double> _findNextLunarEclipseSwissEph(double startJd, double latitude, double longitude) async {
    try {
      logger.d('使用Swiss Ephemeris swe_lun_eclipse_when_loc搜索月蝕，起始儒略日: $startJd');

      // 創建地理位置對象
      final geoPos = GeoPosition(longitude, latitude, 0.0); // 經度, 緯度, 海拔高度

      // 使用真正的Swiss Ephemeris函數搜索月蝕
      final eclipseInfo = Sweph.swe_lun_eclipse_when_loc(
        startJd,
        SwephFlag.SEFLG_SWIEPH, // 使用Swiss Ephemeris
        geoPos, // 觀測者地理位置
        false, // 向前搜索
      );

      if (eclipseInfo.times?.isNotEmpty == true && eclipseInfo.times![0] > 0) {
        final eclipseJd = eclipseInfo.times![0]; // 蝕甚時間
        logger.d('Swiss Ephemeris找到月蝕事件，儒略日: $eclipseJd, 類型: ${eclipseInfo.eclipseType}');

        return eclipseJd;
      }

      logger.d('Swiss Ephemeris未找到月蝕事件');
      return -1; // 沒有找到月蝕
    } catch (e) {
      logger.e('Swiss Ephemeris查找月蝕失敗: $e');
      // 如果Swiss Ephemeris調用失敗，回退到我們的實現
      return await _findNextLunarEclipseBackup(startJd, latitude, longitude);
    }
  }

  /// 使用Swiss Ephemeris原理檢查日蝕
  Future<double> _checkSolarEclipseWithSwissEph(double jd, double latitude, double longitude) async {
    try {
      // 基於Swiss Ephemeris原理的日蝕檢測
      // 1. 計算太陽與月亮的位置與本影路徑
      // 2. 計算月亮交點，推估蝕帶與地影是否交集
      // 3. 判斷該交集區域是否覆蓋地球表面

      final dateTime = await JulianDateUtils.julianDayToDateTime(jd, latitude, longitude);
      final planets = await _astrologyService.calculatePlanetPositions(
        dateTime,
        latitude: latitude,
        longitude: longitude,
      );

      // 找到太陽和月亮的位置
      PlanetPosition? sunPos;
      PlanetPosition? moonPos;

      for (final planet in planets) {
        if (planet.name == '太陽') {
          sunPos = planet;
        } else if (planet.name == '月亮') {
          moonPos = planet;
        }
      }

      if (sunPos != null && moonPos != null) {
        // 1. 計算太陽月亮的黃經差（最重要的指標）
        final longitudeDiff = (moonPos.longitude - sunPos.longitude).abs();
        final normalizedDiff = longitudeDiff > 180 ? 360 - longitudeDiff : longitudeDiff;

        // 2. 計算月亮的黃緯（影響蝕相類型）
        final moonLatitude = moonPos.latitude;

        // 3. 計算月亮到交點的距離（影響蝕相發生概率）
        final nodeDistance = _calculateDistanceToNode(moonPos.longitude);

        // 4. 使用Swiss Ephemeris原理的判斷條件
        // 日蝕發生條件：
        // - 黃經差小於2度（合相附近）
        // - 月亮黃緯絕對值小於1.5度（接近黃道）
        // - 月亮距離交點小於18度（蝕限範圍內）

        if (normalizedDiff < 2.0 &&
            moonLatitude.abs() < 1.5 &&
            nodeDistance < 18.0) {

          logger.d('檢測到可能的日蝕：黃經差=${normalizedDiff.toStringAsFixed(2)}°, '
                  '月亮黃緯=${moonLatitude.toStringAsFixed(2)}°, '
                  '交點距離=${nodeDistance.toStringAsFixed(2)}°');

          // 使用牛頓法精確求解觸蝕時間
          return await _refineEclipseTimeWithNewtonMethod(jd, latitude, longitude, true);
        }
      }

      return -1;
    } catch (e) {
      logger.e('Swiss Ephemeris日蝕檢測失敗: $e');
      return -1;
    }
  }

  /// 檢查指定日期是否有月蝕
  Future<double> _checkLunarEclipseAtDate(double jd, double latitude, double longitude) async {
    try {
      // 計算太陽和月亮的位置
      final dateTime = await JulianDateUtils.julianDayToDateTime(jd, latitude, longitude);
      final planets = await _astrologyService.calculatePlanetPositions(
        dateTime,
        latitude: latitude,
        longitude: longitude,
      );

      // 找到太陽和月亮的位置
      PlanetPosition? sunPos;
      PlanetPosition? moonPos;

      for (final planet in planets) {
        if (planet.name == '太陽') {
          sunPos = planet;
        } else if (planet.name == '月亮') {
          moonPos = planet;
        }
      }

      if (sunPos != null && moonPos != null) {
        // 計算太陽和月亮的角距離（月蝕時應該是180度左右）
        final angularDistance = _calculateAngularDistance(
          sunPos.longitude,
          sunPos.latitude,
          moonPos.longitude,
          moonPos.latitude
        );

        // 如果角距離接近180度，可能是月蝕
        if ((angularDistance - 180).abs() < 2.0) { // 178-182度之間
          // 進一步精確計算
          return await _refineLunarEclipseTime(jd, latitude, longitude);
        }
      }

      return -1;
    } catch (e) {
      logger.e('檢查月蝕失敗: $e');
      return -1;
    }
  }

  /// 精確計算日蝕時間
  Future<double> _refineSolarEclipseTime(double approximateJd, double latitude, double longitude) async {
    try {
      // 在大概時間前後搜索最精確的日蝕時間
      double bestJd = approximateJd;
      double minDistance = double.infinity;

      // 在前後12小時內搜索，每小時檢查一次
      for (double offset = -0.5; offset <= 0.5; offset += 1/24) {
        final testJd = approximateJd + offset;
        final dateTime = await JulianDateUtils.julianDayToDateTime(testJd, latitude, longitude);
        final planets = await _astrologyService.calculatePlanetPositions(
          dateTime,
          latitude: latitude,
          longitude: longitude,
        );

        // 找到太陽和月亮的位置
        PlanetPosition? sunPos;
        PlanetPosition? moonPos;

        for (final planet in planets) {
          if (planet.name == '太陽') {
            sunPos = planet;
          } else if (planet.name == '月亮') {
            moonPos = planet;
          }
        }

        if (sunPos != null && moonPos != null) {
          final distance = _calculateAngularDistance(
            sunPos.longitude,
            sunPos.latitude,
            moonPos.longitude,
            moonPos.latitude
          );

          if (distance < minDistance) {
            minDistance = distance;
            bestJd = testJd;
          }
        }
      }

      return bestJd;
    } catch (e) {
      logger.e('精確計算日蝕時間失敗: $e');
      return approximateJd;
    }
  }

  /// 精確計算月蝕時間
  Future<double> _refineLunarEclipseTime(double approximateJd, double latitude, double longitude) async {
    try {
      // 在大概時間前後搜索最精確的月蝕時間
      double bestJd = approximateJd;
      double minDistanceFrom180 = double.infinity;

      // 在前後12小時內搜索，每小時檢查一次
      for (double offset = -0.5; offset <= 0.5; offset += 1/24) {
        final testJd = approximateJd + offset;
        final dateTime = await JulianDateUtils.julianDayToDateTime(testJd, latitude, longitude);
        final planets = await _astrologyService.calculatePlanetPositions(
          dateTime,
          latitude: latitude,
          longitude: longitude,
        );

        // 找到太陽和月亮的位置
        PlanetPosition? sunPos;
        PlanetPosition? moonPos;

        for (final planet in planets) {
          if (planet.name == '太陽') {
            sunPos = planet;
          } else if (planet.name == '月亮') {
            moonPos = planet;
          }
        }

        if (sunPos != null && moonPos != null) {
          final distance = _calculateAngularDistance(
            sunPos.longitude,
            sunPos.latitude,
            moonPos.longitude,
            moonPos.latitude
          );

          final distanceFrom180 = (distance - 180).abs();

          if (distanceFrom180 < minDistanceFrom180) {
            minDistanceFrom180 = distanceFrom180;
            bestJd = testJd;
          }
        }
      }

      return bestJd;
    } catch (e) {
      logger.e('精確計算月蝕時間失敗: $e');
      return approximateJd;
    }
  }

  /// 獲取日蝕詳細信息（使用Swiss Ephemeris）
  Future<Map<String, dynamic>?> _getSolarEclipseDetails(double eclipseJd, double latitude, double longitude) async {
    try {
      final eclipseDateTime = await JulianDateUtils.julianDayToDateTime(eclipseJd, latitude, longitude);
      final utcDateTime = eclipseDateTime.toUtc();

      // 嘗試使用Swiss Ephemeris獲取詳細信息
      EclipseType eclipseType;
      double magnitude;
      bool isVisible;
      Duration duration;

      try {
        // 重新調用Swiss Ephemeris獲取完整信息
        final eclipseInfo = Sweph.swe_sol_eclipse_when_glob(
          eclipseJd - 1, // 從前一天開始搜索以確保找到這個日蝕
          SwephFlag.SEFLG_SWIEPH,
          EclipseFlag.SE_ECL_ALLTYPES_SOLAR,
          false,
        );

        if (eclipseInfo.times?.isNotEmpty == true && (eclipseInfo.times![0] - eclipseJd).abs() < 1.0) {
          // 根據Swiss Ephemeris的結果確定類型
          eclipseType = _convertSwissEphemerisEclipseType(eclipseInfo.eclipseType ?? EclipseFlag.SE_ECL_PARTIAL, true);

          // 從attributes數組獲取蝕分（如果可用）
          magnitude = eclipseInfo.attributes?.isNotEmpty == true ? eclipseInfo.attributes![0] : 0.5;

          // 計算持續時間（從times數組）
          if (eclipseInfo.times!.length >= 4) {
            final startTime = eclipseInfo.times![1]; // 初虧
            final endTime = eclipseInfo.times![3]; // 復圓
            duration = Duration(minutes: ((endTime - startTime) * 1440).round());
          } else {
            duration = const Duration(minutes: 3); // 默認持續時間
          }

          isVisible = true; // Swiss Ephemeris找到的日蝕默認可見
        } else {
          throw Exception('Swiss Ephemeris未找到對應的日蝕');
        }
      } catch (e) {
        logger.w('Swiss Ephemeris獲取詳細信息失敗，使用備用計算: $e');
        // 回退到我們的計算方法
        magnitude = await _calculateEclipseMagnitude(eclipseJd, latitude, longitude, true);
        eclipseType = _determineSolarEclipseTypeByMagnitude(magnitude);
        isVisible = await _isEclipseVisibleAtLocation(eclipseJd, latitude, longitude, true);
        duration = await _calculateEclipseDuration(eclipseJd, latitude, longitude, true);
      }

      return {
        'dateTime': eclipseDateTime,
        'utcTime': utcDateTime,
        'type': eclipseType,
        'magnitude': magnitude,
        'isVisible': isVisible,
        'duration': duration,
        'description': _getEclipseDescription(eclipseType, isVisible, latitude, longitude),
      };
    } catch (e) {
      logger.e('獲取日蝕詳細信息失敗: $e');
      return null;
    }
  }

  /// 獲取月蝕詳細信息（使用Swiss Ephemeris）
  Future<Map<String, dynamic>?> _getLunarEclipseDetails(double eclipseJd, double latitude, double longitude) async {
    try {
      final eclipseDateTime = await JulianDateUtils.julianDayToDateTime(eclipseJd, latitude, longitude);
      final utcDateTime = eclipseDateTime.toUtc();

      // 嘗試使用Swiss Ephemeris獲取詳細信息
      EclipseType eclipseType;
      double magnitude;
      bool isVisible;
      Duration duration;

      try {
        // 創建地理位置對象
        final geoPos = GeoPosition(longitude, latitude, 0.0); // 經度, 緯度, 海拔高度

        // 重新調用Swiss Ephemeris獲取完整信息
        final eclipseInfo = Sweph.swe_lun_eclipse_when_loc(
          eclipseJd - 1, // 從前一天開始搜索以確保找到這個月蝕
          SwephFlag.SEFLG_SWIEPH,
          geoPos,
          false,
        );

        if (eclipseInfo.times?.isNotEmpty == true && (eclipseInfo.times![0] - eclipseJd).abs() < 1.0) {
          // 根據Swiss Ephemeris的結果確定類型
          eclipseType = _convertSwissEphemerisEclipseType(eclipseInfo.eclipseType, false);

          // 從attributes數組獲取蝕分（如果可用）
          magnitude = eclipseInfo.attributes?.isNotEmpty == true ? eclipseInfo.attributes![0] : 0.5;

          // 計算持續時間（從times數組）
          if (eclipseInfo.times!.length >= 4) {
            final startTime = eclipseInfo.times![1]; // 半影食始
            final endTime = eclipseInfo.times![3]; // 半影食終
            duration = Duration(minutes: ((endTime - startTime) * 1440).round());
          } else {
            duration = const Duration(minutes: 60); // 默認持續時間
          }

          isVisible = true; // Swiss Ephemeris找到的月蝕在指定地點一定可見
        } else {
          throw Exception('Swiss Ephemeris未找到對應的月蝕');
        }
      } catch (e) {
        logger.w('Swiss Ephemeris獲取月蝕詳細信息失敗，使用備用計算: $e');
        // 回退到我們的計算方法
        magnitude = await _calculateEclipseMagnitude(eclipseJd, latitude, longitude, false);
        eclipseType = _determineLunarEclipseTypeByMagnitude(magnitude);
        isVisible = await _isEclipseVisibleAtLocation(eclipseJd, latitude, longitude, false);
        duration = await _calculateEclipseDuration(eclipseJd, latitude, longitude, false);
      }

      return {
        'dateTime': eclipseDateTime,
        'utcTime': utcDateTime,
        'type': eclipseType,
        'magnitude': magnitude,
        'isVisible': isVisible,
        'duration': duration,
        'description': _getEclipseDescription(eclipseType, isVisible, latitude, longitude),
      };
    } catch (e) {
      logger.e('獲取月蝕詳細信息失敗: $e');
      return null;
    }
  }

  /// 計算角距離
  double _calculateAngularDistance(double lon1, double lat1, double lon2, double lat2) {
    // 將度數轉換為弧度
    final lat1Rad = lat1 * (3.14159265359 / 180);
    final lat2Rad = lat2 * (3.14159265359 / 180);
    final deltaLonRad = (lon2 - lon1) * (3.14159265359 / 180);

    // 使用球面三角學計算角距離
    final a = sin((lat2Rad - lat1Rad) / 2);
    final b = sin(deltaLonRad / 2);
    final c = a * a + cos(lat1Rad) * cos(lat2Rad) * b * b;
    final distance = 2 * asin(sqrt(c));

    // 轉換回度數
    return distance * (180 / 3.14159265359);
  }

  /// 計算月亮到交點的距離（Swiss Ephemeris原理）
  double _calculateDistanceToNode(double moonLongitude) {
    // 月亮交點的平均位置計算
    // 這是一個簡化的計算，實際Swiss Ephemeris會使用更精確的交點位置

    // 月亮交點的平均運動：每年約退行19.35度
    // 這裡使用一個近似的交點位置計算

    // 假設某個參考時間的北交點位置（這應該從Swiss Ephemeris獲取）
    const double referenceNodeLongitude = 0.0; // 簡化假設

    // 計算月亮到最近交點的距離
    final distanceToNorthNode = (moonLongitude - referenceNodeLongitude).abs();
    final distanceToSouthNode = (moonLongitude - (referenceNodeLongitude + 180)).abs();

    // 返回到最近交點的距離
    final minDistance = min(distanceToNorthNode, distanceToSouthNode);
    return minDistance > 180 ? 360 - minDistance : minDistance;
  }

  /// 使用牛頓法精確求解蝕相時間（Swiss Ephemeris原理）
  Future<double> _refineEclipseTimeWithNewtonMethod(
    double approximateJd,
    double latitude,
    double longitude,
    bool isSolar
  ) async {
    try {
      // 牛頓法迭代求解最精確的蝕相時間
      // 這是Swiss Ephemeris使用的數值方法

      double currentJd = approximateJd;
      const double tolerance = 1e-8; // 精度要求（約0.001秒）
      const int maxIterations = 10;

      for (int i = 0; i < maxIterations; i++) {
        // 計算當前時間的函數值和導數
        final result = await _calculateEclipseFunction(currentJd, latitude, longitude, isSolar);
        final functionValue = result['value'] as double;
        final derivative = result['derivative'] as double;

        // 檢查收斂條件
        if (functionValue.abs() < tolerance) {
          logger.d('牛頓法收斂，迭代次數: $i, 最終儒略日: $currentJd');
          return currentJd;
        }

        // 牛頓法更新
        if (derivative.abs() > 1e-12) {
          currentJd = currentJd - functionValue / derivative;
        } else {
          // 導數太小，使用小步長搜索
          break;
        }
      }

      logger.d('牛頓法未完全收斂，返回近似值: $currentJd');
      return currentJd;

    } catch (e) {
      logger.e('牛頓法精確計算失敗: $e');
      return approximateJd;
    }
  }

  /// 計算蝕相函數值和導數（用於牛頓法）
  Future<Map<String, double>> _calculateEclipseFunction(
    double jd,
    double latitude,
    double longitude,
    bool isSolar
  ) async {
    try {
      const double deltaT = 1.0 / 1440.0; // 1分鐘的儒略日差

      // 計算當前時間的蝕相指標
      final currentValue = await _getEclipseIndicator(jd, latitude, longitude, isSolar);

      // 計算導數（數值微分）
      final futureValue = await _getEclipseIndicator(jd + deltaT, latitude, longitude, isSolar);
      final derivative = (futureValue - currentValue) / deltaT;

      return {
        'value': currentValue,
        'derivative': derivative,
      };
    } catch (e) {
      logger.e('計算蝕相函數失敗: $e');
      return {'value': 0.0, 'derivative': 1.0};
    }
  }

  /// 計算蝕相指標（用於牛頓法優化）
  Future<double> _getEclipseIndicator(
    double jd,
    double latitude,
    double longitude,
    bool isSolar
  ) async {
    try {
      final dateTime = await JulianDateUtils.julianDayToDateTime(jd, latitude, longitude);
      final planets = await _astrologyService.calculatePlanetPositions(
        dateTime,
        latitude: latitude,
        longitude: longitude,
      );

      // 找到太陽和月亮的位置
      PlanetPosition? sunPos;
      PlanetPosition? moonPos;

      for (final planet in planets) {
        if (planet.name == '太陽') {
          sunPos = planet;
        } else if (planet.name == '月亮') {
          moonPos = planet;
        }
      }

      if (sunPos != null && moonPos != null) {
        if (isSolar) {
          // 日蝕指標：太陽月亮黃經差（目標是0）
          final longitudeDiff = (moonPos.longitude - sunPos.longitude).abs();
          return longitudeDiff > 180 ? 360 - longitudeDiff : longitudeDiff;
        } else {
          // 月蝕指標：太陽月亮黃經差與180度的差值（目標是0）
          final longitudeDiff = (moonPos.longitude - sunPos.longitude).abs();
          final normalizedDiff = longitudeDiff > 180 ? 360 - longitudeDiff : longitudeDiff;
          return (normalizedDiff - 180).abs();
        }
      }

      return 999.0; // 無效值
    } catch (e) {
      logger.e('計算蝕相指標失敗: $e');
      return 999.0;
    }
  }

  /// 計算蝕分
  Future<double> _calculateEclipseMagnitude(double jd, double latitude, double longitude, bool isSolar) async {
    try {
      final dateTime = await JulianDateUtils.julianDayToDateTime(jd, latitude, longitude);
      final planets = await _astrologyService.calculatePlanetPositions(
        dateTime,
        latitude: latitude,
        longitude: longitude,
      );

      // 找到太陽和月亮的位置
      PlanetPosition? sunPos;
      PlanetPosition? moonPos;

      for (final planet in planets) {
        if (planet.name == '太陽') {
          sunPos = planet;
        } else if (planet.name == '月亮') {
          moonPos = planet;
        }
      }

      if (sunPos != null && moonPos != null) {
        final distance = _calculateAngularDistance(
          sunPos.longitude,
          sunPos.latitude,
          moonPos.longitude,
          moonPos.latitude
        );

        if (isSolar) {
          // 日蝕蝕分：距離越小，蝕分越大
          return (2.0 - distance).clamp(0.0, 1.2);
        } else {
          // 月蝕蝕分：距離180度越近，蝕分越大
          final distanceFrom180 = (distance - 180).abs();
          return (2.0 - distanceFrom180).clamp(0.0, 1.8);
        }
      }

      return 0.0;
    } catch (e) {
      logger.e('計算蝕分失敗: $e');
      return 0.0;
    }
  }

  /// 根據蝕分判斷日蝕類型
  EclipseType _determineSolarEclipseTypeByMagnitude(double magnitude) {
    if (magnitude >= 1.0) {
      return EclipseType.solarTotal;
    } else if (magnitude >= 0.95) {
      return EclipseType.solarAnnular;
    } else if (magnitude >= 0.1) {
      return EclipseType.solarPartial;
    } else {
      return EclipseType.solarPartial;
    }
  }

  /// 根據蝕分判斷月蝕類型
  EclipseType _determineLunarEclipseTypeByMagnitude(double magnitude) {
    if (magnitude >= 1.0) {
      return EclipseType.lunarTotal;
    } else if (magnitude >= 0.1) {
      return EclipseType.lunarPartial;
    } else {
      return EclipseType.lunarPenumbral;
    }
  }

  /// 判斷蝕相在指定地點是否可見
  Future<bool> _isEclipseVisibleAtLocation(double jd, double latitude, double longitude, bool isSolar) async {
    try {
      // 簡化的可見性判斷
      // 實際實現應該考慮地球的幾何形狀和蝕相的路徑

      if (isSolar) {
        // 日蝕的可見性更受地理位置限制
        return true; // 簡化處理
      } else {
        // 月蝕在夜間可見的地區都能看到
        final dateTime = await JulianDateUtils.julianDayToDateTime(jd, latitude, longitude);
        final hour = dateTime.hour;

        // 簡單判斷：如果是夜間時間（18:00-06:00），則可見
        return hour >= 18 || hour <= 6;
      }
    } catch (e) {
      logger.e('判斷蝕相可見性失敗: $e');
      return false;
    }
  }

  /// 計算蝕相持續時間
  Future<Duration> _calculateEclipseDuration(double jd, double latitude, double longitude, bool isSolar) async {
    try {
      // 簡化的持續時間計算
      final magnitude = await _calculateEclipseMagnitude(jd, latitude, longitude, isSolar);

      if (isSolar) {
        // 日蝕持續時間通常較短
        final minutes = (magnitude * 7).clamp(1, 7); // 1-7分鐘
        return Duration(minutes: minutes.round());
      } else {
        // 月蝕持續時間較長
        final minutes = (magnitude * 100).clamp(30, 100); // 30-100分鐘
        return Duration(minutes: minutes.round());
      }
    } catch (e) {
      logger.e('計算蝕相持續時間失敗: $e');
      return const Duration(minutes: 1);
    }
  }

  /// 獲取蝕相描述
  String _getEclipseDescription(EclipseType eclipseType, bool isVisible, double latitude, double longitude) {
    final typeDescription = AstroEvent.getEclipseDisplayName(eclipseType);
    final visibilityDescription = isVisible ? '在此地點可見' : '在此地點不可見';

    // 根據地理位置提供更詳細的描述
    String locationDescription = '';
    if (latitude >= 22.0 && latitude <= 26.0 && longitude >= 120.0 && longitude <= 122.0) {
      locationDescription = '台灣地區';
    } else if (latitude >= 20.0 && latitude <= 50.0 && longitude >= 100.0 && longitude <= 140.0) {
      locationDescription = '東亞地區';
    } else {
      locationDescription = '當前位置';
    }

    return '$typeDescription，$locationDescription$visibilityDescription';
  }

  /// 獲取蝕相的重要度
  int _getEclipseImportance(EclipseType eclipseType) {
    switch (eclipseType) {
      case EclipseType.solarTotal:
        return 5; // 日全蝕最重要
      case EclipseType.lunarTotal:
        return 4; // 月全蝕次之
      case EclipseType.solarAnnular:
      case EclipseType.solarHybrid:
        return 3; // 日環蝕和混合蝕
      case EclipseType.solarPartial:
      case EclipseType.lunarPartial:
        return 2; // 偏蝕
      case EclipseType.lunarPenumbral:
        return 1; // 半影月蝕最低
    }
  }

  /// 格式化日月蝕時間顯示
  String _formatEclipseTime(DateTime dateTime) {
    return '${dateTime.year}年${dateTime.month}月${dateTime.day}日 '
           '${dateTime.hour.toString().padLeft(2, '0')}:'
           '${dateTime.minute.toString().padLeft(2, '0')}:'
           '${dateTime.second.toString().padLeft(2, '0')}';
  }

  /// 轉換Swiss Ephemeris的蝕相類型到我們的枚舉
  EclipseType _convertSwissEphemerisEclipseType(EclipseFlag swissType, bool isSolar) {
    try {
      if (isSolar) {
        // 日蝕類型轉換
        switch (swissType.value) {
          case 1: // SE_ECL_TOTAL
            return EclipseType.solarTotal;
          case 2: // SE_ECL_ANNULAR
            return EclipseType.solarAnnular;
          case 4: // SE_ECL_PARTIAL
            return EclipseType.solarPartial;
          case 8: // SE_ECL_ANNULAR_TOTAL (混合蝕)
            return EclipseType.solarHybrid;
          default:
            logger.w('未知的Swiss Ephemeris日蝕類型: ${swissType.value}');
            return EclipseType.solarPartial;
        }
      } else {
        // 月蝕類型轉換
        switch (swissType.value) {
          case 1: // SE_ECL_TOTAL
            return EclipseType.lunarTotal;
          case 4: // SE_ECL_PARTIAL
            return EclipseType.lunarPartial;
          case 16: // SE_ECL_PENUMBRAL
            return EclipseType.lunarPenumbral;
          default:
            logger.w('未知的Swiss Ephemeris月蝕類型: ${swissType.value}');
            return EclipseType.lunarPartial;
        }
      }
    } catch (e) {
      logger.e('轉換Swiss Ephemeris蝕相類型失敗: $e');
      return isSolar ? EclipseType.solarPartial : EclipseType.lunarPartial;
    }
  }

  /// 檢查日蝕在指定地點的可見性
  Future<bool> _checkSolarEclipseVisibility(double eclipseJd, double latitude, double longitude) async {
    try {
      // 這裡可以使用Swiss Ephemeris的swe_sol_eclipse_how函數
      // 但由於我們沒有直接的API，使用簡化的可見性判斷

      // 簡化判斷：大部分日蝕在某些地區都是可見的
      // 實際實現應該使用Swiss Ephemeris的詳細可見性計算
      return true;
    } catch (e) {
      logger.e('檢查日蝕可見性失敗: $e');
      return true; // 默認可見
    }
  }

  /// 備用日蝕搜索方法（當Swiss Ephemeris調用失敗時使用）
  Future<double> _findNextSolarEclipseBackup(double startJd, double latitude, double longitude) async {
    try {
      logger.d('使用備用方法搜索日蝕');

      double searchJd = startJd;
      const maxSearchDays = 1095; // 搜索3年內的日蝕
      const searchStep = 14; // 14天步長

      while (searchJd < startJd + maxSearchDays) {
        final eclipseResult = await _checkSolarEclipseWithSwissEph(searchJd, latitude, longitude);

        if (eclipseResult > 0) {
          logger.d('備用方法找到日蝕事件，儒略日: $eclipseResult');
          return eclipseResult;
        }

        searchJd += searchStep;
      }

      return -1;
    } catch (e) {
      logger.e('備用日蝕搜索失敗: $e');
      return -1;
    }
  }

  /// 備用月蝕搜索方法（當Swiss Ephemeris調用失敗時使用）
  Future<double> _findNextLunarEclipseBackup(double startJd, double latitude, double longitude) async {
    try {
      logger.d('使用備用方法搜索月蝕');

      double searchJd = startJd;
      const maxSearchDays = 1095; // 搜索3年內的月蝕
      const searchStep = 15; // 15天步長

      while (searchJd < startJd + maxSearchDays) {
        final eclipseResult = await _checkLunarEclipseAtDate(searchJd, latitude, longitude);

        if (eclipseResult > 0) {
          logger.d('備用方法找到月蝕事件，儒略日: $eclipseResult');
          return eclipseResult;
        }

        searchJd += searchStep;
      }

      return -1;
    } catch (e) {
      logger.e('備用月蝕搜索失敗: $e');
      return -1;
    }
  }
}
