import 'package:flutter/material.dart';

/// 星象事件類型枚舉
enum AstroEventType {
  moonPhase,        // 月相
  seasonChange,     // 節氣變化
  planetAspect,     // 行星相位
  planetSignChange, // 行星換座
  planetRetrograde, // 行星逆行
  eclipse,          // 日月食
}

/// 月相類型枚舉
enum MoonPhaseType {
  newMoon,      // 新月
  waxingCrescent, // 娥眉月
  firstQuarter, // 上弦月
  waxingGibbous, // 盈凸月
  fullMoon,     // 滿月
  waningGibbous, // 虧凸月
  lastQuarter,  // 下弦月
  waningCrescent, // 殘月
}

/// 日月食類型枚舉
enum EclipseType {
  solarTotal,     // 日全食
  solarAnnular,   // 日環食
  solarPartial,   // 日偏食
  solarHybrid,    // 日混合食
  lunarTotal,     // 月全食
  lunarPartial,   // 月偏食
  lunarPenumbral, // 月半影食
}

/// 星象事件模型
class AstroEvent {
  final String id;
  final String title;
  final String description;
  final DateTime dateTime;
  final AstroEventType type;
  final Color color;
  final IconData icon;
  final int importance; // 重要度 1-5
  final Map<String, dynamic>? additionalData;

  const AstroEvent({
    required this.id,
    required this.title,
    required this.description,
    required this.dateTime,
    required this.type,
    required this.color,
    required this.icon,
    this.importance = 1,
    this.additionalData,
  });

  /// 獲取事件類型的顯示名稱
  String get typeDisplayName {
    switch (type) {
      case AstroEventType.moonPhase:
        return '月相';
      case AstroEventType.seasonChange:
        return '節氣';
      case AstroEventType.planetAspect:
        return '相位';
      case AstroEventType.planetSignChange:
        return '換座';
      case AstroEventType.planetRetrograde:
        return '逆行';
      case AstroEventType.eclipse:
        return '食相';
    }
  }

  /// 獲取重要度顏色
  Color get importanceColor {
    switch (importance) {
      case 5:
        return Colors.red;
      case 4:
        return Colors.orange;
      case 3:
        return Colors.amber;
      case 2:
        return Colors.blue;
      case 1:
      default:
        return Colors.grey;
    }
  }

  /// 獲取月相類型的顯示名稱
  static String getMoonPhaseDisplayName(MoonPhaseType phase) {
    switch (phase) {
      case MoonPhaseType.newMoon:
        return '新月';
      case MoonPhaseType.waxingCrescent:
        return '娥眉月';
      case MoonPhaseType.firstQuarter:
        return '上弦月';
      case MoonPhaseType.waxingGibbous:
        return '盈凸月';
      case MoonPhaseType.fullMoon:
        return '滿月';
      case MoonPhaseType.waningGibbous:
        return '虧凸月';
      case MoonPhaseType.lastQuarter:
        return '下弦月';
      case MoonPhaseType.waningCrescent:
        return '殘月';
    }
  }

  /// 獲取月相圖標
  static IconData getMoonPhaseIcon(MoonPhaseType phase) {
    switch (phase) {
      case MoonPhaseType.newMoon:
        return Icons.brightness_1;
      case MoonPhaseType.waxingCrescent:
        return Icons.brightness_2;
      case MoonPhaseType.firstQuarter:
        return Icons.brightness_3;
      case MoonPhaseType.waxingGibbous:
        return Icons.brightness_4;
      case MoonPhaseType.fullMoon:
        return Icons.brightness_7;
      case MoonPhaseType.waningGibbous:
        return Icons.brightness_4;
      case MoonPhaseType.lastQuarter:
        return Icons.brightness_3;
      case MoonPhaseType.waningCrescent:
        return Icons.brightness_2;
    }
  }

  /// 獲取日月食顯示名稱
  static String getEclipseDisplayName(EclipseType eclipseType) {
    switch (eclipseType) {
      case EclipseType.solarTotal:
        return '日全食';
      case EclipseType.solarAnnular:
        return '日環食';
      case EclipseType.solarPartial:
        return '日偏食';
      case EclipseType.solarHybrid:
        return '日混合食';
      case EclipseType.lunarTotal:
        return '月全食';
      case EclipseType.lunarPartial:
        return '月偏食';
      case EclipseType.lunarPenumbral:
        return '月半影食';
    }
  }

  /// 獲取日月食圖標
  static IconData getEclipseIcon(EclipseType eclipseType) {
    switch (eclipseType) {
      case EclipseType.solarTotal:
      case EclipseType.solarAnnular:
      case EclipseType.solarPartial:
      case EclipseType.solarHybrid:
        return Icons.wb_sunny_outlined; // 日食圖標
      case EclipseType.lunarTotal:
      case EclipseType.lunarPartial:
      case EclipseType.lunarPenumbral:
        return Icons.nightlight_round; // 月食圖標
    }
  }

  /// 獲取日月食顏色
  static Color getEclipseColor(EclipseType eclipseType) {
    switch (eclipseType) {
      case EclipseType.solarTotal:
        return Colors.black87; // 日全食 - 黑色
      case EclipseType.solarAnnular:
        return Colors.orange; // 日環食 - 橙色
      case EclipseType.solarPartial:
        return Colors.amber; // 日偏食 - 琥珀色
      case EclipseType.solarHybrid:
        return Colors.deepOrange; // 日混合食 - 深橙色
      case EclipseType.lunarTotal:
        return Colors.red.shade800; // 月全食 - 深紅色
      case EclipseType.lunarPartial:
        return Colors.red.shade400; // 月偏食 - 紅色
      case EclipseType.lunarPenumbral:
        return Colors.grey.shade600; // 月半影食 - 灰色
    }
  }

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is AstroEvent &&
          runtimeType == other.runtimeType &&
          id == other.id;

  @override
  int get hashCode => id.hashCode;

  @override
  String toString() {
    return 'AstroEvent{id: $id, title: $title, dateTime: $dateTime, type: $type}';
  }
}
