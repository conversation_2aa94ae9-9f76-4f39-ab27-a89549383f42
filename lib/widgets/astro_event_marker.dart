import 'package:flutter/material.dart';

import '../models/astro_event.dart';
import '../ui/AppTheme.dart';

/// 星象事件標記組件
/// 用於在日曆上標記星象事件
class AstroEventMarker extends StatelessWidget {
  final List<AstroEvent> events;
  final double size;
  final bool showCount;

  const AstroEventMarker({
    super.key,
    required this.events,
    this.size = 6.0,
    this.showCount = false,
  });

  @override
  Widget build(BuildContext context) {
    if (events.isEmpty) return const SizedBox.shrink();

    // 按重要度排序事件
    final sortedEvents = List<AstroEvent>.from(events)
      ..sort((a, b) => b.importance.compareTo(a.importance));

    // 獲取最重要的事件
    final mostImportantEvent = sortedEvents.first;

    return Container(
      margin: const EdgeInsets.only(top: 2),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          // 主要事件標記
          Container(
            width: size,
            height: size,
            decoration: BoxDecoration(
              color: mostImportantEvent.color,
              shape: BoxShape.circle,
              border: Border.all(
                color: Colors.white,
                width: 0.5,
              ),
            ),
          ),

          // 如果有多個事件，顯示額外的小點
          if (events.length > 1) ...[
            const SizedBox(width: 2),
            Container(
              width: size * 0.7,
              height: size * 0.7,
              decoration: BoxDecoration(
                color: events.length > 2
                    ? sortedEvents[1].color
                    : Colors.grey.withValues(alpha: 0.5),
                shape: BoxShape.circle,
                border: Border.all(
                  color: Colors.white,
                  width: 0.3,
                ),
              ),
            ),
          ],

          // 如果有超過2個事件，顯示數量
          if (events.length > 2 && showCount) ...[
            const SizedBox(width: 2),
            Container(
              padding: const EdgeInsets.symmetric(horizontal: 2, vertical: 1),
              decoration: BoxDecoration(
                color: AppColors.royalIndigo,
                borderRadius: BorderRadius.circular(6),
              ),
              child: Text(
                '+${events.length - 2}',
                style: const TextStyle(
                  color: Colors.white,
                  fontSize: 8,
                  fontWeight: FontWeight.bold,
                ),
              ),
            ),
          ],
        ],
      ),
    );
  }
}

/// 事件類型篩選器組件
class EventTypeFilter extends StatelessWidget {
  final Set<AstroEventType> selectedTypes;
  final Function(AstroEventType) onToggle;
  final Function(AstroEventType) getDisplayName;
  final Function(AstroEventType) getIcon;
  final Function(AstroEventType) getColor;

  const EventTypeFilter({
    super.key,
    required this.selectedTypes,
    required this.onToggle,
    required this.getDisplayName,
    required this.getIcon,
    required this.getColor,
  });

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: const EdgeInsets.all(12),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          const Text(
            '事件篩選',
            style: TextStyle(
              fontSize: 16,
              fontWeight: FontWeight.bold,
              color: AppColors.royalIndigo,
            ),
          ),
          const SizedBox(height: 8),
          Wrap(
            spacing: 8,
            runSpacing: 4,
            children: AstroEventType.values.map((type) {
              final isSelected = selectedTypes.contains(type);
              return FilterChip(
                label: Row(
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    Icon(
                      getIcon(type),
                      size: 16,
                      color: isSelected ? Colors.white : getColor(type),
                    ),
                    const SizedBox(width: 4),
                    Text(
                      getDisplayName(type),
                      style: TextStyle(
                        color: isSelected ? Colors.white : getColor(type),
                        fontSize: 12,
                      ),
                    ),
                  ],
                ),
                selected: isSelected,
                onSelected: (_) => onToggle(type),
                selectedColor: getColor(type),
                backgroundColor: getColor(type).withValues(alpha: 0.1),
                checkmarkColor: Colors.white,
                side: BorderSide(
                  color: getColor(type),
                  width: 1,
                ),
              );
            }).toList(),
          ),
        ],
      ),
    );
  }
}

/// 日期事件詳情組件
class DailyEventDetail extends StatelessWidget {
  final DateTime selectedDate;
  final List<AstroEvent> events;
  final Function(AstroEvent)? onTapEvent;

  const DailyEventDetail({
    super.key,
    required this.selectedDate,
    required this.events,
    this.onTapEvent,
  });

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // 日期標題
          Row(
            children: [
              const Icon(
                Icons.calendar_today,
                color: AppColors.royalIndigo,
                size: 20,
              ),
              const SizedBox(width: 8),
              Text(
                '${selectedDate.year}年${selectedDate.month}月${selectedDate.day}日',
                style: const TextStyle(
                  fontSize: 18,
                  fontWeight: FontWeight.bold,
                  color: AppColors.royalIndigo,
                ),
              ),
            ],
          ),
          const SizedBox(height: 12),

          // 事件列表
          if (events.isEmpty) ...[
            const Center(
              child: Padding(
                padding: EdgeInsets.all(20),
                child: Text(
                  '今日無特殊星象事件',
                  style: TextStyle(
                    color: Colors.grey,
                    fontSize: 14,
                  ),
                ),
              ),
            ),
          ] else ...[
            ...events.map((event) => _buildEventItem(event)),
          ],
        ],
      ),
    );
  }

  Widget _buildEventItem(AstroEvent event) {
    return InkWell(
      onTap: () => onTapEvent?.call(event),
      borderRadius: BorderRadius.circular(8),
      child: Container(
        margin: const EdgeInsets.only(bottom: 8),
        padding: const EdgeInsets.all(12),
        decoration: BoxDecoration(
          color: event.color.withValues(alpha: 0.1),
          borderRadius: BorderRadius.circular(8),
          border: Border.all(
            color: event.color.withValues(alpha: 0.3),
            width: 1,
          ),
        ),
        child: Row(
          children: [
            // 事件圖標
            Container(
              padding: const EdgeInsets.all(6),
              decoration: BoxDecoration(
                color: event.color,
                shape: BoxShape.circle,
              ),
              child: Icon(
                event.icon,
                color: Colors.white,
                size: 16,
              ),
            ),
            const SizedBox(width: 12),

            // 事件內容
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  // 事件標題
                  Text(
                    event.title,
                    style: const TextStyle(
                      fontSize: 14,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                  const SizedBox(height: 2),

                  // 事件描述
                  Text(
                    event.description,
                    style: TextStyle(
                      fontSize: 12,
                      color: Colors.grey[600],
                    ),
                  ),

                  // 事件時間
                  if (event.dateTime.hour != 0 || event.dateTime.minute != 0) ...[
                    const SizedBox(height: 2),
                    Text(
                      '${event.dateTime.hour.toString().padLeft(2, '0')}:${event.dateTime.minute.toString().padLeft(2, '0')}',
                      style: TextStyle(
                        fontSize: 11,
                        color: Colors.grey[500],
                      ),
                    ),
                  ],
                ],
              ),
            ),

            // 重要度指示器和箭頭
            Row(
              mainAxisSize: MainAxisSize.min,
              children: [
                Container(
                  padding: const EdgeInsets.symmetric(horizontal: 6, vertical: 2),
                  decoration: BoxDecoration(
                    color: event.importanceColor,
                    borderRadius: BorderRadius.circular(10),
                  ),
                  child: Text(
                    '${event.importance}',
                    style: const TextStyle(
                      color: Colors.white,
                      fontSize: 10,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                ),
                const SizedBox(width: 8),
                Icon(
                  Icons.arrow_forward_ios,
                  size: 14,
                  color: Colors.grey[400],
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }
}
