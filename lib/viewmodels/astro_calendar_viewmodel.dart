import 'package:flutter/material.dart';
import 'package:table_calendar/table_calendar.dart';

import '../models/astro_event.dart';
import '../models/birth_data.dart';
import '../services/astro_calendar_service.dart';
import '../utils/LoggerUtils.dart';

/// 星象日曆頁面的ViewModel
class AstroCalendarViewModel extends ChangeNotifier {
  final AstroCalendarService _calendarService = AstroCalendarService();

  // 狀態變數
  bool _isLoading = false;
  DateTime _focusedDay = DateTime.now();
  DateTime _selectedDay = DateTime.now();
  CalendarFormat _calendarFormat = CalendarFormat.month;

  // 事件數據
  Map<DateTime, List<AstroEvent>> _events = {};
  List<AstroEvent> _selectedDayEvents = [];

  // 篩選設置
  Set<AstroEventType> _selectedEventTypes = AstroEventType.values.toSet();

  // 緩存機制
  final Map<String, Map<DateTime, List<AstroEvent>>> _monthlyCache = {};
  DateTime? _lastLoadedMonth;

  // 位置設置
  double _latitude = 25.0;
  double _longitude = 121.0;
  BirthData? _natalPerson;

  // Getters
  bool get isLoading => _isLoading;
  DateTime get focusedDay => _focusedDay;
  DateTime get selectedDay => _selectedDay;
  CalendarFormat get calendarFormat => _calendarFormat;
  Map<DateTime, List<AstroEvent>> get events => _events;
  List<AstroEvent> get selectedDayEvents => _selectedDayEvents;
  Set<AstroEventType> get selectedEventTypes => _selectedEventTypes;
  double get latitude => _latitude;
  double get longitude => _longitude;
  BirthData? get natalPerson => _natalPerson;

  /// 設置載入狀態
  void setLoading(bool loading) {
    _isLoading = loading;
    notifyListeners();
  }

  /// 設置焦點日期
  void setFocusedDay(DateTime day) {
    _focusedDay = day;
    notifyListeners();

    // 載入該月的事件
    loadMonthlyEvents(day.year, day.month);
  }

  /// 設置選中日期
  void setSelectedDay(DateTime day) {
    _selectedDay = day;
    _updateSelectedDayEvents();
    notifyListeners();
  }

  /// 設置日曆格式
  void setCalendarFormat(CalendarFormat format) {
    _calendarFormat = format;
    notifyListeners();
  }

  /// 設置位置
  void setLocation(double latitude, double longitude) {
    _latitude = latitude;
    _longitude = longitude;
    notifyListeners();

    // 重新載入當前月份的事件
    loadMonthlyEvents(_focusedDay.year, _focusedDay.month);
  }

  /// 設置本命盤人物
  void setNatalPerson(BirthData? person) {
    _natalPerson = person;
    notifyListeners();

    // 重新載入當前月份的事件
    loadMonthlyEvents(_focusedDay.year, _focusedDay.month);
  }

  /// 切換事件類型篩選
  void toggleEventType(AstroEventType type) {
    if (_selectedEventTypes.contains(type)) {
      _selectedEventTypes.remove(type);
    } else {
      _selectedEventTypes.add(type);
    }
    _updateSelectedDayEvents();
    notifyListeners();
  }

  /// 載入指定月份的事件
  Future<void> loadMonthlyEvents(int year, int month) async {
    try {
      setLoading(true);

      final monthlyEvents = await _calendarService.getMonthlyEvents(
        year,
        month,
        latitude: _latitude,
        longitude: _longitude,
        natalPerson: _natalPerson,
      );

      // 將事件按日期分組
      final eventsMap = <DateTime, List<AstroEvent>>{};
      for (final event in monthlyEvents) {
        final eventDate = DateTime(
          event.dateTime.year,
          event.dateTime.month,
          event.dateTime.day,
        );

        if (eventsMap[eventDate] == null) {
          eventsMap[eventDate] = [];
        }
        eventsMap[eventDate]!.add(event);
      }

      _events = eventsMap;
      _updateSelectedDayEvents();

      logger.i('載入 $year年$month月 星象事件完成: ${monthlyEvents.length} 個');
    } catch (e) {
      logger.e('載入月度星象事件失敗: $e');
    } finally {
      setLoading(false);
    }
  }

  /// 載入指定日期的事件
  Future<void> loadDailyEvents(DateTime date) async {
    try {
      final dailyEvents = await _calendarService.getDailyEvents(
        date,
        latitude: _latitude,
        longitude: _longitude,
        natalPerson: _natalPerson,
      );

      final eventDate = DateTime(date.year, date.month, date.day);
      _events[eventDate] = dailyEvents;

      if (isSameDay(date, _selectedDay)) {
        _updateSelectedDayEvents();
      }

      notifyListeners();
      logger.d('載入 ${date.toString().split(' ')[0]} 星象事件完成: ${dailyEvents.length} 個');
    } catch (e) {
      logger.e('載入日度星象事件失敗: $e');
    }
  }

  /// 獲取指定日期的事件
  List<AstroEvent> getEventsForDay(DateTime day) {
    final eventDate = DateTime(day.year, day.month, day.day);
    final dayEvents = _events[eventDate] ?? [];

    // 根據篩選設置過濾事件
    return dayEvents.where((event) => _selectedEventTypes.contains(event.type)).toList();
  }

  /// 更新選中日期的事件
  void _updateSelectedDayEvents() {
    _selectedDayEvents = getEventsForDay(_selectedDay);
  }

  /// 初始化
  Future<void> initialize() async {
    await loadMonthlyEvents(_focusedDay.year, _focusedDay.month);
  }

  /// 刷新當前月份
  Future<void> refresh() async {
    await loadMonthlyEvents(_focusedDay.year, _focusedDay.month);
  }

  /// 設置事件數據（測試用）
  void setEvents(Map<DateTime, List<AstroEvent>> events) {
    _events = events;
    _updateSelectedDayEvents();
    notifyListeners();
  }

  /// 獲取事件類型的顯示名稱
  String getEventTypeDisplayName(AstroEventType type) {
    switch (type) {
      case AstroEventType.moonPhase:
        return '月相';
      case AstroEventType.seasonChange:
        return '節氣';
      case AstroEventType.planetAspect:
        return '相位';
      case AstroEventType.planetSignChange:
        return '換座';
      case AstroEventType.planetRetrograde:
        return '逆行';
      case AstroEventType.eclipse:
        return '食相';
    }
  }

  /// 獲取事件類型的圖標
  IconData getEventTypeIcon(AstroEventType type) {
    switch (type) {
      case AstroEventType.moonPhase:
        return Icons.brightness_4;
      case AstroEventType.seasonChange:
        return Icons.wb_sunny;
      case AstroEventType.planetAspect:
        return Icons.timeline;
      case AstroEventType.planetSignChange:
        return Icons.swap_horiz;
      case AstroEventType.planetRetrograde:
        return Icons.refresh;
      case AstroEventType.eclipse:
        return Icons.brightness_2;
    }
  }

  /// 獲取事件類型的顏色
  Color getEventTypeColor(AstroEventType type) {
    switch (type) {
      case AstroEventType.moonPhase:
        return Colors.indigo;
      case AstroEventType.seasonChange:
        return Colors.green;
      case AstroEventType.planetAspect:
        return Colors.orange;
      case AstroEventType.planetSignChange:
        return Colors.blue;
      case AstroEventType.planetRetrograde:
        return Colors.red;
      case AstroEventType.eclipse:
        return Colors.purple;
    }
  }
}
