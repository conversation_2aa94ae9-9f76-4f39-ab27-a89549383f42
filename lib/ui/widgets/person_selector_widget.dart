import 'package:astreal/models/birth_data.dart';
import 'package:astreal/models/chart_data.dart';
import 'package:astreal/models/chart_type.dart';
import 'package:astreal/ui/AppTheme.dart';
import 'package:astreal/ui/widgets/person_selection_dialog.dart';
import 'package:flutter/material.dart';
import 'package:provider/provider.dart';

import '../../viewmodels/chart_viewmodel.dart';
import '../pages/chart_page.dart';

/// 人物選擇器元件，用於顯示當前選中的人物信息並提供選擇/更改人物的功能
class PersonSelectorWidget extends StatelessWidget {
  /// 當前選中的人物
  final BirthData? selectedPerson;

  /// 人物列表
  final List<BirthData> personList;

  /// 選擇人物時的回調函數
  final Function(BirthData) onPersonSelected;

  /// 編輯人物時的回調函數
  final Function(BirthData)? onEditPerson;

  /// 是否顯示編輯按鈕
  final bool showEditButton;

  /// 卡片標題
  final String title;

  /// 卡片圖標
  final IconData icon;

  /// 圖標顏色
  final Color iconColor;

  /// 對話框標題
  final String dialogTitle;

  /// 對話框按鈕顏色
  final Color dialogButtonColor;

  /// 是否正在加載數據
  final bool isLoading;

  /// 格式化日期時間的函數
  final String Function(DateTime) formatDateTime;

  /// 構造函數
  const PersonSelectorWidget({
    Key? key,
    required this.selectedPerson,
    required this.personList,
    required this.onPersonSelected,
    required this.formatDateTime,
    this.onEditPerson,
    this.showEditButton = true,
    this.title = '個人資訊',
    this.icon = Icons.person,
    this.iconColor = AppColors.solarAmber,
    this.dialogTitle = '選擇人物',
    this.dialogButtonColor = AppColors.royalIndigo,
    this.isLoading = false,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Card(
      elevation: 2,
      margin: EdgeInsets.zero,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
      child: InkWell(
        onTap: () {
          if (selectedPerson != null) {
            _navigateToNatalChart(context, selectedPerson!);
          } else {
            _showPersonSelectionDialog(context);
          }
        },
        borderRadius: BorderRadius.circular(12),
        child: Padding(
          padding: const EdgeInsets.all(12.0),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // 標題行 - 緊湊設計
              Row(
                children: [
                  // 標題和圖標
                  Icon(icon, color: iconColor, size: 18),
                  const SizedBox(width: 6),
                  Text(
                    title,
                    style: const TextStyle(
                      fontSize: 15,
                      fontWeight: FontWeight.bold,
                      color: AppColors.textDark,
                    ),
                  ),
                  const Spacer(),
                  // 緊湊的操作按鈕
                  if (selectedPerson != null) ...[
                    _buildCompactButton(
                      icon: Icons.visibility,
                      tooltip: '查看星盤',
                      onPressed: () => _navigateToNatalChart(context, selectedPerson!),
                    ),
                    const SizedBox(width: 4),
                  ],
                  if (showEditButton && selectedPerson != null && onEditPerson != null) ...[
                    _buildCompactButton(
                      icon: Icons.edit,
                      tooltip: '編輯資料',
                      onPressed: () => onEditPerson!(selectedPerson!),
                    ),
                    const SizedBox(width: 4),
                  ],
                  _buildCompactButton(
                    icon: Icons.person_search,
                    tooltip: '選擇人物',
                    onPressed: () => _showPersonSelectionDialog(context),
                  ),
                ],
              ),
              const SizedBox(height: 8),
              // 內容區域
              if (isLoading)
                const Center(
                  child: Padding(
                    padding: EdgeInsets.symmetric(vertical: 16),
                    child: CircularProgressIndicator(
                      strokeWidth: 2,
                      valueColor: AlwaysStoppedAnimation<Color>(AppColors.royalIndigo),
                    ),
                  ),
                )
              else if (selectedPerson == null)
                _buildEmptyState()
              else
                _buildPersonInfo(selectedPerson!),
            ],
          ),
        ),
      ),
    );
  }

  /// 構建緊湊按鈕
  Widget _buildCompactButton({
    required IconData icon,
    required String tooltip,
    required VoidCallback onPressed,
  }) {
    return Container(
      width: 28,
      height: 28,
      decoration: BoxDecoration(
        color: AppColors.royalIndigo.withValues(alpha: 0.1),
        borderRadius: BorderRadius.circular(6),
        border: Border.all(
          color: AppColors.royalIndigo.withValues(alpha: 0.3),
          width: 1,
        ),
      ),
      child: IconButton(
        icon: Icon(icon, size: 14),
        onPressed: onPressed,
        tooltip: tooltip,
        color: AppColors.royalIndigo,
        padding: EdgeInsets.zero,
        constraints: const BoxConstraints(),
        splashRadius: 14,
      ),
    );
  }

  /// 構建空狀態
  Widget _buildEmptyState() {
    return Container(
      padding: const EdgeInsets.symmetric(vertical: 12),
      child: Row(
        children: [
          Icon(
            Icons.person_add_alt_1,
            size: 24,
            color: AppColors.royalIndigo.withValues(alpha: 0.4),
          ),
          const SizedBox(width: 8),
          const Expanded(
            child: Text(
              '點擊選擇人物以顯示個人資訊',
              style: TextStyle(
                color: AppColors.textMedium,
                fontSize: 13,
              ),
            ),
          ),
        ],
      ),
    );
  }

  /// 構建人物信息區域
  Widget _buildPersonInfo(BirthData person) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        _buildCompactInfoRow(Icons.person_outline, person.name),
        const SizedBox(height: 4),
        _buildCompactInfoRow(Icons.calendar_today, formatDateTime(person.birthDate)),
        const SizedBox(height: 4),
        _buildCompactInfoRow(Icons.location_on, person.birthPlace),
        if (person.notes != null && person.notes!.isNotEmpty) ...[
          const SizedBox(height: 4),
          _buildCompactInfoRow(Icons.note, person.notes!),
        ],
      ],
    );
  }

  /// 構建緊湊信息行
  Widget _buildCompactInfoRow(IconData icon, String value) {
    return Row(
      children: [
        Icon(icon, size: 14, color: AppColors.textMedium),
        const SizedBox(width: 6),
        Expanded(
          child: Text(
            value,
            style: const TextStyle(
              fontSize: 13,
              color: AppColors.textDark,
            ),
            maxLines: 1,
            overflow: TextOverflow.ellipsis,
          ),
        ),
      ],
    );
  }

  /// 構建信息行（保留舊版本以兼容性）
  Widget _buildInfoRow(IconData icon, String label, String value) {
    return Row(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Icon(icon, size: 16, color: AppColors.textMedium),
        const SizedBox(width: 8),
        SizedBox(
          width: 60,
          child: Text(
            label,
            style: const TextStyle(
              color: AppColors.textMedium,
              fontSize: 14,
            ),
          ),
        ),
        const SizedBox(width: 8),
        Expanded(
          child: Text(
            value,
            style: const TextStyle(
              fontSize: 14,
              color: AppColors.textDark,
            ),
          ),
        ),
      ],
    );
  }

  /// 顯示人物選擇對話框
  Future<void> _showPersonSelectionDialog(BuildContext context) async {
    if (personList.isEmpty) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(content: Text('沒有可用的出生資料，請先新增出生資料')),
      );
      return;
    }

    final BirthData? result = await showDialog<BirthData>(
      context: context,
      builder: (BuildContext context) {
        return PersonSelectionDialog(
          personList: personList,
          selectedPerson: selectedPerson,
          title: dialogTitle,
          buttonColor: dialogButtonColor,
        );
      },
    );

    if (result != null) {
      onPersonSelected(result);
    }
  }

  /// 導航到本命盤頁面
  void _navigateToNatalChart(BuildContext context, BirthData birthData) {
    // 創建 ChartData 對象
    final chartData = ChartData(
      chartType: ChartType.natal,
      primaryPerson: birthData,
    );

    // 導航到星盤頁面
    Navigator.push(
      context,
      MaterialPageRoute(
        builder: (context) => ChangeNotifierProvider(
          create: (_) => ChartViewModel.withChartData(
            initialChartData: chartData,
            context: context,
          ),
          child: ChartPage(chartData: chartData),
        ),
      ),
    );
  }
}
