import 'package:flutter/material.dart';
import 'package:flutter_datetime_picker_plus/flutter_datetime_picker_plus.dart' as DatePicker;
import 'package:provider/provider.dart';
import 'package:table_calendar/table_calendar.dart';

import '../../models/astro_event.dart';
import '../../ui/AppTheme.dart';
import '../../viewmodels/astro_calendar_viewmodel.dart';
import '../../widgets/astro_event_marker.dart';
import '../../widgets/styled_card.dart';
import 'astro_event_detail_page.dart';

/// 星象日曆頁面
class AstroCalendarPage extends StatefulWidget {
  const AstroCalendarPage({super.key});

  @override
  State<AstroCalendarPage> createState() => _AstroCalendarPageState();
}

class _AstroCalendarPageState extends State<AstroCalendarPage> {
  late AstroCalendarViewModel _viewModel;

  @override
  void initState() {
    super.initState();
    _viewModel = AstroCalendarViewModel();
    _viewModel.initialize();
  }

  @override
  void dispose() {
    _viewModel.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return ChangeNotifierProvider.value(
      value: _viewModel,
      child: Scaffold(
        appBar: AppBar(
          title: const Text('星象日曆'),
          backgroundColor: AppColors.royalIndigo,
          foregroundColor: Colors.white,
          actions: [
            // 篩選按鈕
            IconButton(
              icon: const Icon(Icons.filter_list),
              onPressed: _showFilterDialog,
            ),
            // 刷新按鈕
            IconButton(
              icon: const Icon(Icons.refresh),
              onPressed: () => _viewModel.refresh(),
            ),
          ],
        ),
        body: Consumer<AstroCalendarViewModel>(
          builder: (context, viewModel, child) {
            return Column(
              children: [
                // 日曆組件
                _buildCalendar(viewModel),

                // 分隔線
                const Divider(height: 1),

                // 選中日期的事件詳情
                Expanded(
                  child: _buildEventDetails(viewModel),
                ),
              ],
            );
          },
        ),
      ),
    );
  }

  /// 構建日曆組件
  Widget _buildCalendar(AstroCalendarViewModel viewModel) {
    return StyledCard(
      margin: const EdgeInsets.all(8),
      child: TableCalendar<AstroEvent>(
        key: ValueKey(viewModel.selectedEventTypes.hashCode), // 添加key確保篩選變化時重建
        firstDay: DateTime.utc(2020, 1, 1),
        lastDay: DateTime.utc(2030, 12, 31),
        focusedDay: viewModel.focusedDay,
        selectedDayPredicate: (day) => isSameDay(viewModel.selectedDay, day),
        calendarFormat: viewModel.calendarFormat,
        eventLoader: viewModel.getEventsForDay,
        startingDayOfWeek: StartingDayOfWeek.monday,

        // 樣式設定
        calendarStyle: const CalendarStyle(
          outsideDaysVisible: false,
          weekendTextStyle: TextStyle(color: Colors.red),
          holidayTextStyle: TextStyle(color: Colors.red),
          selectedDecoration: BoxDecoration(
            color: AppColors.royalIndigo,
            shape: BoxShape.circle,
          ),
          todayDecoration: BoxDecoration(
            color: AppColors.solarAmber,
            shape: BoxShape.circle,
          ),
          markerDecoration: BoxDecoration(
            color: AppColors.royalIndigo,
            shape: BoxShape.circle,
          ),
        ),

        // 標題樣式
        headerStyle: const HeaderStyle(
          formatButtonVisible: true,
          titleCentered: true,
          formatButtonShowsNext: false,
          formatButtonDecoration: BoxDecoration(
            color: AppColors.royalIndigo,
            borderRadius: BorderRadius.all(Radius.circular(12.0)),
          ),
          formatButtonTextStyle: TextStyle(
            color: Colors.white,
          ),
        ),

        // 自定義標題構建器
        calendarBuilders: CalendarBuilders<AstroEvent>(
          // 自定義標題
          headerTitleBuilder: (context, day) {
            return GestureDetector(
              onTap: () => _showDatePicker(viewModel),
              child: Container(
                padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
                decoration: BoxDecoration(
                  color: AppColors.royalIndigo.withOpacity(0.1),
                  borderRadius: BorderRadius.circular(6),
                  border: Border.all(color: AppColors.royalIndigo.withOpacity(0.3)),
                ),
                child: IntrinsicWidth(
                  child: Row(
                    mainAxisSize: MainAxisSize.min,
                    children: [
                      Flexible(
                        child: Text(
                          '${day.year}年${day.month}月',
                          style: const TextStyle(
                            fontSize: 14,
                            fontWeight: FontWeight.bold,
                            color: AppColors.royalIndigo,
                          ),
                          overflow: TextOverflow.ellipsis,
                        ),
                      ),
                      const SizedBox(width: 2),
                      const Icon(
                        Icons.arrow_drop_down,
                        color: AppColors.royalIndigo,
                        size: 18,
                      ),
                    ],
                  ),
                ),
              ),
            );
          },
          // 自定義事件標記
          markerBuilder: (context, day, events) {
            if (events.isNotEmpty) {
              return AstroEventMarker(
                events: events,
                size: 6.0,
                showCount: true,
              );
            }
            return null;
          },
        ),

        // 事件回調
        onDaySelected: (selectedDay, focusedDay) {
          viewModel.setSelectedDay(selectedDay);
          viewModel.setFocusedDay(focusedDay);
        },

        onFormatChanged: (format) {
          viewModel.setCalendarFormat(format);
        },

        onPageChanged: (focusedDay) {
          viewModel.setFocusedDay(focusedDay);
        },
      ),
    );
  }

  /// 構建事件詳情
  Widget _buildEventDetails(AstroCalendarViewModel viewModel) {
    if (viewModel.isLoading) {
      return _buildLoadingWidget();
    }

    return SingleChildScrollView(
      child: DailyEventDetail(
        selectedDate: viewModel.selectedDay,
        events: viewModel.selectedDayEvents,
        onTapEvent: (event) => _navigateToEventDetail(event, viewModel),
      ),
    );
  }

  /// 構建載入中的UI
  Widget _buildLoadingWidget() {
    return Container(
      padding: const EdgeInsets.all(32),
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          // 自定義載入動畫
          Stack(
            alignment: Alignment.center,
            children: [
              // 外圈 - 慢速旋轉
              SizedBox(
                width: 80,
                height: 80,
                child: CircularProgressIndicator(
                  strokeWidth: 2,
                  valueColor: AlwaysStoppedAnimation<Color>(
                    AppColors.royalIndigo.withOpacity(0.3),
                  ),
                ),
              ),
              // 內圈 - 快速旋轉
              SizedBox(
                width: 50,
                height: 50,
                child: CircularProgressIndicator(
                  strokeWidth: 3,
                  valueColor: const AlwaysStoppedAnimation<Color>(
                    AppColors.royalIndigo,
                  ),
                ),
              ),
              // 中心圖標
              const Icon(
                Icons.auto_awesome,
                color: AppColors.solarAmber,
                size: 24,
              ),
            ],
          ),

          const SizedBox(height: 24),

          // 載入文字
          const Text(
            '正在計算星象事件...',
            style: TextStyle(
              fontSize: 16,
              fontWeight: FontWeight.w500,
              color: AppColors.royalIndigo,
            ),
          ),

          const SizedBox(height: 8),

          // 提示文字
          Text(
            '包含月相、節氣、相位、換座等事件',
            style: TextStyle(
              fontSize: 12,
              color: Colors.grey[600],
            ),
          ),
        ],
      ),
    );
  }

  /// 顯示日期選擇器
  void _showDatePicker(AstroCalendarViewModel viewModel) {
    DatePicker.DatePicker.showDatePicker(
      context,
      showTitleActions: true,
      minTime: DateTime(1800, 1, 1),
      maxTime: DateTime(3000, 12, 31),
      onConfirm: (selectedDate) {
        // 確定按鈕的回調
        viewModel.setFocusedDay(selectedDate);
        viewModel.setSelectedDay(selectedDate);
      },
      onCancel: () {
        // 取消按鈕的回調（可選）
        print('用戶取消了日期選擇');
      },
      currentTime: viewModel.focusedDay,
      locale: DatePicker.LocaleType.tw,
      theme: const DatePicker.DatePickerTheme(
        backgroundColor: Colors.white,
        headerColor: Colors.white,
        itemStyle: TextStyle(
          color: Colors.black87,
          fontSize: 18,
          fontWeight: FontWeight.w500,
        ),
        doneStyle: TextStyle(
          color: AppColors.royalIndigo,
          fontSize: 16,
          fontWeight: FontWeight.bold,
        ),
        cancelStyle: TextStyle(
          color: Colors.grey,
          fontSize: 16,
        ),
        titleHeight: 50.0,
        containerHeight: 240.0,
        itemHeight: 40.0,
      ),
    );
  }

  /// 顯示篩選對話框
  void _showFilterDialog() {
    // 在對話框外部獲取 viewModel 引用，避免 Provider 作用域問題
    final viewModel = _viewModel;

    showDialog(
      context: context,
      builder: (dialogContext) => StatefulBuilder(
        builder: (context, setState) => AlertDialog(
          title: Row(
            children: [
              const Icon(Icons.filter_list, color: AppColors.royalIndigo),
              const SizedBox(width: 8),
              const Text('事件篩選'),
              const Spacer(),
              Text(
                '${viewModel.selectedEventTypes.length}/${AstroEventType.values.length}',
                style: TextStyle(
                  fontSize: 12,
                  color: Colors.grey[600],
                ),
              ),
            ],
          ),
          content: SizedBox(
            width: double.maxFinite,
            child: Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                EventTypeFilter(
                  selectedTypes: viewModel.selectedEventTypes,
                  onToggle: (type) {
                    viewModel.toggleEventType(type);
                    setState(() {}); // 更新對話框狀態
                  },
                  getDisplayName: viewModel.getEventTypeDisplayName,
                  getIcon: viewModel.getEventTypeIcon,
                  getColor: viewModel.getEventTypeColor,
                ),
                const SizedBox(height: 16),
                // 添加全選/全不選按鈕
                Row(
                  mainAxisAlignment: MainAxisAlignment.spaceEvenly,
                  children: [
                    TextButton.icon(
                      onPressed: () {
                        // 全選
                        for (final type in AstroEventType.values) {
                          if (!viewModel.selectedEventTypes.contains(type)) {
                            viewModel.toggleEventType(type);
                          }
                        }
                        setState(() {}); // 更新對話框狀態
                      },
                      icon: const Icon(Icons.select_all, size: 16),
                      label: const Text('全選'),
                    ),
                    TextButton.icon(
                      onPressed: () {
                        // 全不選
                        final typesToRemove = List<AstroEventType>.from(viewModel.selectedEventTypes);
                        for (final type in typesToRemove) {
                          viewModel.toggleEventType(type);
                        }
                        setState(() {}); // 更新對話框狀態
                      },
                      icon: const Icon(Icons.clear_all, size: 16),
                      label: const Text('全不選'),
                    ),
                  ],
                ),
              ],
            ),
          ),
          actions: [
            TextButton(
              onPressed: () => Navigator.of(dialogContext).pop(),
              child: const Text('關閉'),
            ),
          ],
        ),
      ),
    );
  }

  /// 導航到事件詳情頁面
  void _navigateToEventDetail(AstroEvent event, AstroCalendarViewModel viewModel) {
    Navigator.push(
      context,
      MaterialPageRoute(
        builder: (context) => AstroEventDetailPage(
          event: event,
          natalPerson: viewModel.natalPerson,
          latitude: viewModel.latitude,
          longitude: viewModel.longitude,
        ),
      ),
    );
  }
}
