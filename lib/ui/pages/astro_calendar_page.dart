import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:table_calendar/table_calendar.dart';

import '../../models/astro_event.dart';
import '../../ui/AppTheme.dart';
import '../../viewmodels/astro_calendar_viewmodel.dart';
import '../../widgets/astro_event_marker.dart';
import '../../widgets/styled_card.dart';
import 'astro_event_detail_page.dart';

/// 星象日曆頁面
class AstroCalendarPage extends StatefulWidget {
  const AstroCalendarPage({super.key});

  @override
  State<AstroCalendarPage> createState() => _AstroCalendarPageState();
}

class _AstroCalendarPageState extends State<AstroCalendarPage> {
  late AstroCalendarViewModel _viewModel;

  @override
  void initState() {
    super.initState();
    _viewModel = AstroCalendarViewModel();
    _viewModel.initialize();
  }

  @override
  void dispose() {
    _viewModel.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return ChangeNotifierProvider.value(
      value: _viewModel,
      child: Scaffold(
        appBar: AppBar(
          title: const Text('星象日曆'),
          backgroundColor: AppColors.royalIndigo,
          foregroundColor: Colors.white,
          actions: [
            // 篩選按鈕
            IconButton(
              icon: const Icon(Icons.filter_list),
              onPressed: _showFilterDialog,
            ),
            // 刷新按鈕
            IconButton(
              icon: const Icon(Icons.refresh),
              onPressed: () => _viewModel.refresh(),
            ),
          ],
        ),
        body: Consumer<AstroCalendarViewModel>(
          builder: (context, viewModel, child) {
            return Column(
              children: [
                // 日曆組件
                _buildCalendar(viewModel),

                // 分隔線
                const Divider(height: 1),

                // 選中日期的事件詳情
                Expanded(
                  child: _buildEventDetails(viewModel),
                ),
              ],
            );
          },
        ),
      ),
    );
  }

  /// 構建日曆組件
  Widget _buildCalendar(AstroCalendarViewModel viewModel) {
    return StyledCard(
      margin: const EdgeInsets.all(8),
      child: TableCalendar<AstroEvent>(
        firstDay: DateTime.utc(2020, 1, 1),
        lastDay: DateTime.utc(2030, 12, 31),
        focusedDay: viewModel.focusedDay,
        selectedDayPredicate: (day) => isSameDay(viewModel.selectedDay, day),
        calendarFormat: viewModel.calendarFormat,
        eventLoader: viewModel.getEventsForDay,
        startingDayOfWeek: StartingDayOfWeek.monday,

        // 樣式設定
        calendarStyle: const CalendarStyle(
          outsideDaysVisible: false,
          weekendTextStyle: TextStyle(color: Colors.red),
          holidayTextStyle: TextStyle(color: Colors.red),
          selectedDecoration: BoxDecoration(
            color: AppColors.royalIndigo,
            shape: BoxShape.circle,
          ),
          todayDecoration: BoxDecoration(
            color: AppColors.solarAmber,
            shape: BoxShape.circle,
          ),
          markerDecoration: BoxDecoration(
            color: AppColors.royalIndigo,
            shape: BoxShape.circle,
          ),
        ),

        // 標題樣式
        headerStyle: const HeaderStyle(
          formatButtonVisible: true,
          titleCentered: true,
          formatButtonShowsNext: false,
          formatButtonDecoration: BoxDecoration(
            color: AppColors.royalIndigo,
            borderRadius: BorderRadius.all(Radius.circular(12.0)),
          ),
          formatButtonTextStyle: TextStyle(
            color: Colors.white,
          ),
        ),

        // 事件回調
        onDaySelected: (selectedDay, focusedDay) {
          viewModel.setSelectedDay(selectedDay);
          viewModel.setFocusedDay(focusedDay);
        },

        onFormatChanged: (format) {
          viewModel.setCalendarFormat(format);
        },

        onPageChanged: (focusedDay) {
          viewModel.setFocusedDay(focusedDay);
        },

        // 自定義事件標記
        calendarBuilders: CalendarBuilders<AstroEvent>(
          markerBuilder: (context, day, events) {
            if (events.isNotEmpty) {
              return AstroEventMarker(
                events: events,
                size: 6.0,
                showCount: true,
              );
            }
            return null;
          },
        ),
      ),
    );
  }

  /// 構建事件詳情
  Widget _buildEventDetails(AstroCalendarViewModel viewModel) {
    if (viewModel.isLoading) {
      return const Center(child: CircularProgressIndicator());
    }

    return SingleChildScrollView(
      child: DailyEventDetail(
        selectedDate: viewModel.selectedDay,
        events: viewModel.selectedDayEvents,
        onTapEvent: (event) => _navigateToEventDetail(event, viewModel),
      ),
    );
  }

  /// 顯示篩選對話框
  void _showFilterDialog() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('事件篩選'),
        content: SizedBox(
          width: double.maxFinite,
          child: EventTypeFilter(
            selectedTypes: _viewModel.selectedEventTypes,
            onToggle: _viewModel.toggleEventType,
            getDisplayName: _viewModel.getEventTypeDisplayName,
            getIcon: _viewModel.getEventTypeIcon,
            getColor: _viewModel.getEventTypeColor,
          ),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('關閉'),
          ),
        ],
      ),
    );
  }

  /// 導航到事件詳情頁面
  void _navigateToEventDetail(AstroEvent event, AstroCalendarViewModel viewModel) {
    Navigator.push(
      context,
      MaterialPageRoute(
        builder: (context) => AstroEventDetailPage(
          event: event,
          natalPerson: viewModel.natalPerson,
          latitude: viewModel.latitude,
          longitude: viewModel.longitude,
        ),
      ),
    );
  }
}
