import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:table_calendar/table_calendar.dart';

import '../../models/astro_event.dart';
import '../../ui/AppTheme.dart';
import '../../viewmodels/astro_calendar_viewmodel.dart';
import '../../widgets/astro_event_marker.dart';
import '../../widgets/styled_card.dart';
import 'astro_event_detail_page.dart';

/// 星象日曆頁面
class AstroCalendarPage extends StatefulWidget {
  const AstroCalendarPage({super.key});

  @override
  State<AstroCalendarPage> createState() => _AstroCalendarPageState();
}

class _AstroCalendarPageState extends State<AstroCalendarPage> {
  late AstroCalendarViewModel _viewModel;

  @override
  void initState() {
    super.initState();
    _viewModel = AstroCalendarViewModel();
    _viewModel.initialize();
  }

  @override
  void dispose() {
    _viewModel.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return ChangeNotifierProvider.value(
      value: _viewModel,
      child: Scaffold(
        appBar: AppBar(
          title: const Text('星象日曆'),
          backgroundColor: AppColors.royalIndigo,
          foregroundColor: Colors.white,
          actions: [
            // 篩選按鈕
            IconButton(
              icon: const Icon(Icons.filter_list),
              onPressed: _showFilterDialog,
            ),
            // 刷新按鈕
            IconButton(
              icon: const Icon(Icons.refresh),
              onPressed: () => _viewModel.refresh(),
            ),
          ],
        ),
        body: Consumer<AstroCalendarViewModel>(
          builder: (context, viewModel, child) {
            return Column(
              children: [
                // 日曆組件
                _buildCalendar(viewModel),

                // 分隔線
                const Divider(height: 1),

                // 選中日期的事件詳情
                Expanded(
                  child: _buildEventDetails(viewModel),
                ),
              ],
            );
          },
        ),
      ),
    );
  }

  /// 構建日曆組件
  Widget _buildCalendar(AstroCalendarViewModel viewModel) {
    return StyledCard(
      margin: const EdgeInsets.all(8),
      child: TableCalendar<AstroEvent>(
        key: ValueKey(viewModel.selectedEventTypes.hashCode), // 添加key確保篩選變化時重建
        firstDay: DateTime.utc(2020, 1, 1),
        lastDay: DateTime.utc(2030, 12, 31),
        focusedDay: viewModel.focusedDay,
        selectedDayPredicate: (day) => isSameDay(viewModel.selectedDay, day),
        calendarFormat: viewModel.calendarFormat,
        eventLoader: viewModel.getEventsForDay,
        startingDayOfWeek: StartingDayOfWeek.monday,

        // 樣式設定
        calendarStyle: const CalendarStyle(
          outsideDaysVisible: false,
          weekendTextStyle: TextStyle(color: Colors.red),
          holidayTextStyle: TextStyle(color: Colors.red),
          selectedDecoration: BoxDecoration(
            color: AppColors.royalIndigo,
            shape: BoxShape.circle,
          ),
          todayDecoration: BoxDecoration(
            color: AppColors.solarAmber,
            shape: BoxShape.circle,
          ),
          markerDecoration: BoxDecoration(
            color: AppColors.royalIndigo,
            shape: BoxShape.circle,
          ),
        ),

        // 標題樣式
        headerStyle: const HeaderStyle(
          formatButtonVisible: true,
          titleCentered: true,
          formatButtonShowsNext: false,
          formatButtonDecoration: BoxDecoration(
            color: AppColors.royalIndigo,
            borderRadius: BorderRadius.all(Radius.circular(12.0)),
          ),
          formatButtonTextStyle: TextStyle(
            color: Colors.white,
          ),
        ),

        // 自定義標題構建器
        calendarBuilders: CalendarBuilders<AstroEvent>(
          // 自定義標題
          headerTitleBuilder: (context, day) {
            return GestureDetector(
              onTap: () => _showDatePicker(viewModel),
              child: Container(
                padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
                decoration: BoxDecoration(
                  color: AppColors.royalIndigo.withOpacity(0.1),
                  borderRadius: BorderRadius.circular(6),
                  border: Border.all(color: AppColors.royalIndigo.withOpacity(0.3)),
                ),
                child: IntrinsicWidth(
                  child: Row(
                    mainAxisSize: MainAxisSize.min,
                    children: [
                      Flexible(
                        child: Text(
                          '${day.year}年${day.month}月',
                          style: const TextStyle(
                            fontSize: 14,
                            fontWeight: FontWeight.bold,
                            color: AppColors.royalIndigo,
                          ),
                          overflow: TextOverflow.ellipsis,
                        ),
                      ),
                      const SizedBox(width: 2),
                      const Icon(
                        Icons.arrow_drop_down,
                        color: AppColors.royalIndigo,
                        size: 18,
                      ),
                    ],
                  ),
                ),
              ),
            );
          },
          // 自定義事件標記
          markerBuilder: (context, day, events) {
            if (events.isNotEmpty) {
              return AstroEventMarker(
                events: events,
                size: 6.0,
                showCount: true,
              );
            }
            return null;
          },
        ),

        // 事件回調
        onDaySelected: (selectedDay, focusedDay) {
          viewModel.setSelectedDay(selectedDay);
          viewModel.setFocusedDay(focusedDay);
        },

        onFormatChanged: (format) {
          viewModel.setCalendarFormat(format);
        },

        onPageChanged: (focusedDay) {
          viewModel.setFocusedDay(focusedDay);
        },
      ),
    );
  }

  /// 構建事件詳情
  Widget _buildEventDetails(AstroCalendarViewModel viewModel) {
    if (viewModel.isLoading) {
      return const Center(child: CircularProgressIndicator());
    }

    return SingleChildScrollView(
      child: DailyEventDetail(
        selectedDate: viewModel.selectedDay,
        events: viewModel.selectedDayEvents,
        onTapEvent: (event) => _navigateToEventDetail(event, viewModel),
      ),
    );
  }

  /// 顯示日期選擇器
  void _showDatePicker(AstroCalendarViewModel viewModel) {
    showDialog(
      context: context,
      builder: (context) => _DatePickerDialog(
        initialDate: viewModel.focusedDay,
        onDateSelected: (selectedDate) {
          viewModel.setFocusedDay(selectedDate);
          viewModel.setSelectedDay(selectedDate);
        },
      ),
    );
  }

  /// 顯示篩選對話框
  void _showFilterDialog() {
    // 在對話框外部獲取 viewModel 引用，避免 Provider 作用域問題
    final viewModel = _viewModel;

    showDialog(
      context: context,
      builder: (dialogContext) => StatefulBuilder(
        builder: (context, setState) => AlertDialog(
          title: Row(
            children: [
              const Icon(Icons.filter_list, color: AppColors.royalIndigo),
              const SizedBox(width: 8),
              const Text('事件篩選'),
              const Spacer(),
              Text(
                '${viewModel.selectedEventTypes.length}/${AstroEventType.values.length}',
                style: TextStyle(
                  fontSize: 12,
                  color: Colors.grey[600],
                ),
              ),
            ],
          ),
          content: SizedBox(
            width: double.maxFinite,
            child: Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                EventTypeFilter(
                  selectedTypes: viewModel.selectedEventTypes,
                  onToggle: (type) {
                    viewModel.toggleEventType(type);
                    setState(() {}); // 更新對話框狀態
                  },
                  getDisplayName: viewModel.getEventTypeDisplayName,
                  getIcon: viewModel.getEventTypeIcon,
                  getColor: viewModel.getEventTypeColor,
                ),
                const SizedBox(height: 16),
                // 添加全選/全不選按鈕
                Row(
                  mainAxisAlignment: MainAxisAlignment.spaceEvenly,
                  children: [
                    TextButton.icon(
                      onPressed: () {
                        // 全選
                        for (final type in AstroEventType.values) {
                          if (!viewModel.selectedEventTypes.contains(type)) {
                            viewModel.toggleEventType(type);
                          }
                        }
                        setState(() {}); // 更新對話框狀態
                      },
                      icon: const Icon(Icons.select_all, size: 16),
                      label: const Text('全選'),
                    ),
                    TextButton.icon(
                      onPressed: () {
                        // 全不選
                        final typesToRemove = List<AstroEventType>.from(viewModel.selectedEventTypes);
                        for (final type in typesToRemove) {
                          viewModel.toggleEventType(type);
                        }
                        setState(() {}); // 更新對話框狀態
                      },
                      icon: const Icon(Icons.clear_all, size: 16),
                      label: const Text('全不選'),
                    ),
                  ],
                ),
              ],
            ),
          ),
          actions: [
            TextButton(
              onPressed: () => Navigator.of(dialogContext).pop(),
              child: const Text('關閉'),
            ),
          ],
        ),
      ),
    );
  }

  /// 導航到事件詳情頁面
  void _navigateToEventDetail(AstroEvent event, AstroCalendarViewModel viewModel) {
    Navigator.push(
      context,
      MaterialPageRoute(
        builder: (context) => AstroEventDetailPage(
          event: event,
          natalPerson: viewModel.natalPerson,
          latitude: viewModel.latitude,
          longitude: viewModel.longitude,
        ),
      ),
    );
  }
}

/// 自定義日期選擇器對話框
class _DatePickerDialog extends StatefulWidget {
  final DateTime initialDate;
  final Function(DateTime) onDateSelected;

  const _DatePickerDialog({
    required this.initialDate,
    required this.onDateSelected,
  });

  @override
  State<_DatePickerDialog> createState() => _DatePickerDialogState();
}

class _DatePickerDialogState extends State<_DatePickerDialog> {
  late int selectedYear;
  late int selectedMonth;
  late int selectedDay;

  // 年份範圍
  static const int minYear = 2020;
  static const int maxYear = 2030;

  @override
  void initState() {
    super.initState();
    selectedYear = widget.initialDate.year;
    selectedMonth = widget.initialDate.month;
    selectedDay = widget.initialDate.day;
  }

  @override
  Widget build(BuildContext context) {
    return AlertDialog(
      title: Row(
        children: [
          const Icon(Icons.calendar_today, color: AppColors.royalIndigo),
          const SizedBox(width: 8),
          const Text('選擇日期'),
          const Spacer(),
          Text(
            '$selectedYear年$selectedMonth月$selectedDay日',
            style: TextStyle(
              fontSize: 14,
              color: Colors.grey[600],
            ),
          ),
        ],
      ),
      content: SizedBox(
        width: double.maxFinite,
        height: 320, // 增加高度以容納所有內容
        child: SingleChildScrollView(
          child: Column(
            children: [
              // 年份選擇
              _buildYearSelector(),
              const SizedBox(height: 12), // 減少間距

              // 月份選擇
              _buildMonthSelector(),
              const SizedBox(height: 12), // 減少間距

              // 日期選擇
              _buildDaySelector(),
              const SizedBox(height: 8), // 底部留一點空間
            ],
          ),
        ),
      ),
      actions: [
        TextButton(
          onPressed: () => Navigator.of(context).pop(),
          child: const Text('取消'),
        ),
        ElevatedButton(
          onPressed: () {
            final selectedDate = DateTime(selectedYear, selectedMonth, selectedDay);
            widget.onDateSelected(selectedDate);
            Navigator.of(context).pop();
          },
          style: ElevatedButton.styleFrom(
            backgroundColor: AppColors.royalIndigo,
            foregroundColor: Colors.white,
          ),
          child: const Text('確定'),
        ),
      ],
    );
  }

  /// 構建年份選擇器
  Widget _buildYearSelector() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        const Text(
          '年份',
          style: TextStyle(
            fontSize: 14,
            fontWeight: FontWeight.bold,
            color: AppColors.royalIndigo,
          ),
        ),
        const SizedBox(height: 6),
        Container(
          height: 50,
          decoration: BoxDecoration(
            border: Border.all(color: AppColors.royalIndigo.withOpacity(0.3)),
            borderRadius: BorderRadius.circular(8),
          ),
          child: ListView.builder(
            scrollDirection: Axis.horizontal,
            itemCount: maxYear - minYear + 1,
            itemBuilder: (context, index) {
              final year = minYear + index;
              final isSelected = year == selectedYear;

              return GestureDetector(
                onTap: () {
                  setState(() {
                    selectedYear = year;
                    // 檢查選中的日期是否在新年份中有效
                    _validateSelectedDate();
                  });
                },
                child: Container(
                  width: 60,
                  margin: const EdgeInsets.symmetric(horizontal: 4, vertical: 6),
                  decoration: BoxDecoration(
                    color: isSelected ? AppColors.royalIndigo : Colors.transparent,
                    borderRadius: BorderRadius.circular(6),
                    border: Border.all(
                      color: isSelected ? AppColors.royalIndigo : Colors.grey.withOpacity(0.3),
                    ),
                  ),
                  child: Center(
                    child: Text(
                      year.toString(),
                      style: TextStyle(
                        color: isSelected ? Colors.white : Colors.black87,
                        fontWeight: isSelected ? FontWeight.bold : FontWeight.normal,
                      ),
                    ),
                  ),
                ),
              );
            },
          ),
        ),
      ],
    );
  }

  /// 構建月份選擇器
  Widget _buildMonthSelector() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        const Text(
          '月份',
          style: TextStyle(
            fontSize: 14,
            fontWeight: FontWeight.bold,
            color: AppColors.royalIndigo,
          ),
        ),
        const SizedBox(height: 6),
        Container(
          height: 50,
          decoration: BoxDecoration(
            border: Border.all(color: AppColors.royalIndigo.withOpacity(0.3)),
            borderRadius: BorderRadius.circular(8),
          ),
          child: ListView.builder(
            scrollDirection: Axis.horizontal,
            itemCount: 12,
            itemBuilder: (context, index) {
              final month = index + 1;
              final isSelected = month == selectedMonth;

              return GestureDetector(
                onTap: () {
                  setState(() {
                    selectedMonth = month;
                    // 檢查選中的日期是否在新月份中有效
                    _validateSelectedDate();
                  });
                },
                child: Container(
                  width: 50,
                  margin: const EdgeInsets.symmetric(horizontal: 4, vertical: 6),
                  decoration: BoxDecoration(
                    color: isSelected ? AppColors.royalIndigo : Colors.transparent,
                    borderRadius: BorderRadius.circular(6),
                    border: Border.all(
                      color: isSelected ? AppColors.royalIndigo : Colors.grey.withOpacity(0.3),
                    ),
                  ),
                  child: Center(
                    child: Text(
                      '${month}月',
                      style: TextStyle(
                        color: isSelected ? Colors.white : Colors.black87,
                        fontWeight: isSelected ? FontWeight.bold : FontWeight.normal,
                        fontSize: 12,
                      ),
                    ),
                  ),
                ),
              );
            },
          ),
        ),
      ],
    );
  }

  /// 構建日期選擇器
  Widget _buildDaySelector() {
    final daysInMonth = DateTime(selectedYear, selectedMonth + 1, 0).day;

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        const Text(
          '日期',
          style: TextStyle(
            fontSize: 14,
            fontWeight: FontWeight.bold,
            color: AppColors.royalIndigo,
          ),
        ),
        const SizedBox(height: 6),
        Container(
          height: 50,
          decoration: BoxDecoration(
            border: Border.all(color: AppColors.royalIndigo.withOpacity(0.3)),
            borderRadius: BorderRadius.circular(8),
          ),
          child: ListView.builder(
            scrollDirection: Axis.horizontal,
            itemCount: daysInMonth,
            itemBuilder: (context, index) {
              final day = index + 1;
              final isSelected = day == selectedDay;

              return GestureDetector(
                onTap: () {
                  setState(() {
                    selectedDay = day;
                  });
                },
                child: Container(
                  width: 40,
                  margin: const EdgeInsets.symmetric(horizontal: 2, vertical: 6),
                  decoration: BoxDecoration(
                    color: isSelected ? AppColors.royalIndigo : Colors.transparent,
                    borderRadius: BorderRadius.circular(6),
                    border: Border.all(
                      color: isSelected ? AppColors.royalIndigo : Colors.grey.withOpacity(0.3),
                    ),
                  ),
                  child: Center(
                    child: Text(
                      day.toString(),
                      style: TextStyle(
                        color: isSelected ? Colors.white : Colors.black87,
                        fontWeight: isSelected ? FontWeight.bold : FontWeight.normal,
                        fontSize: 12,
                      ),
                    ),
                  ),
                ),
              );
            },
          ),
        ),
      ],
    );
  }

  /// 驗證選中的日期是否有效
  void _validateSelectedDate() {
    final daysInMonth = DateTime(selectedYear, selectedMonth + 1, 0).day;
    if (selectedDay > daysInMonth) {
      selectedDay = daysInMonth;
    }
  }
}
