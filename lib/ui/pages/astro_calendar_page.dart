import 'package:flutter/material.dart';
import 'package:flutter_datetime_picker_plus/flutter_datetime_picker_plus.dart' as DatePicker;
import 'package:provider/provider.dart';
import 'package:table_calendar/table_calendar.dart';

import '../../models/astro_event.dart';
import '../../ui/AppTheme.dart';
import '../../viewmodels/astro_calendar_viewmodel.dart';
import '../../widgets/astro_event_marker.dart';
import '../../widgets/styled_card.dart';
import 'astro_event_detail_page.dart';

/// 星象日曆頁面
class AstroCalendarPage extends StatefulWidget {
  const AstroCalendarPage({super.key});

  @override
  State<AstroCalendarPage> createState() => _AstroCalendarPageState();
}

class _AstroCalendarPageState extends State<AstroCalendarPage> {
  late AstroCalendarViewModel _viewModel;
  bool _isInitialized = false;

  @override
  void initState() {
    super.initState();
    _viewModel = AstroCalendarViewModel();
    // 延遲初始化以提升頁面載入速度
    _initializeDelayed();
  }

  /// 延遲初始化
  void _initializeDelayed() async {
    // 等待頁面渲染完成
    await Future.delayed(const Duration(milliseconds: 100));

    if (mounted && !_isInitialized) {
      _isInitialized = true;
      await _viewModel.initialize();
    }
  }

  @override
  void dispose() {
    _viewModel.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return ChangeNotifierProvider.value(
      value: _viewModel,
      child: Scaffold(
        appBar: AppBar(
          title: const Text('星象日曆'),
          backgroundColor: AppColors.royalIndigo,
          foregroundColor: Colors.white,
          actions: [
            // 篩選按鈕
            IconButton(
              icon: const Icon(Icons.filter_list),
              onPressed: _showFilterDialog,
            ),
            // 刷新按鈕
            IconButton(
              icon: const Icon(Icons.refresh),
              onPressed: () => _viewModel.refresh(),
            ),
          ],
        ),
        body: Consumer<AstroCalendarViewModel>(
          builder: (context, viewModel, child) {
            return Column(
              children: [
                // 日曆組件
                _buildCalendar(viewModel),

                // 分隔線
                const Divider(height: 1),

                // 選中日期的事件詳情
                Expanded(
                  child: _buildEventDetails(viewModel),
                ),
              ],
            );
          },
        ),
      ),
    );
  }

  /// 構建日曆組件
  Widget _buildCalendar(AstroCalendarViewModel viewModel) {
    return StyledCard(
      margin: const EdgeInsets.all(8),
<<<<<<< HEAD
      child: Stack(
        children: [
          TableCalendar<AstroEvent>(
            key: ValueKey(viewModel.selectedEventTypes.hashCode), // 添加key確保篩選變化時重建
            firstDay: DateTime.utc(2020, 1, 1),
            lastDay: DateTime.utc(2030, 12, 31),
            focusedDay: viewModel.focusedDay,
            selectedDayPredicate: (day) => isSameDay(viewModel.selectedDay, day),
            calendarFormat: viewModel.calendarFormat,
            eventLoader: viewModel.getEventsForDay,
            startingDayOfWeek: StartingDayOfWeek.monday,
=======
      child: TableCalendar<AstroEvent>(
        firstDay: DateTime.utc(2020, 1, 1),
        lastDay: DateTime.utc(2030, 12, 31),
        focusedDay: viewModel.focusedDay,
        selectedDayPredicate: (day) => isSameDay(viewModel.selectedDay, day),
        calendarFormat: viewModel.calendarFormat,
        eventLoader: viewModel.getEventsForDay,
        startingDayOfWeek: StartingDayOfWeek.monday,
>>>>>>> parent of 1fa7a03 (feat(日曆): 增強日月食功能及修復篩選問題)

        // 樣式設定
        calendarStyle: const CalendarStyle(
          outsideDaysVisible: false,
          weekendTextStyle: TextStyle(color: Colors.red),
          holidayTextStyle: TextStyle(color: Colors.red),
          selectedDecoration: BoxDecoration(
            color: AppColors.royalIndigo,
            shape: BoxShape.circle,
          ),
          todayDecoration: BoxDecoration(
            color: AppColors.solarAmber,
            shape: BoxShape.circle,
          ),
          markerDecoration: BoxDecoration(
            color: AppColors.royalIndigo,
            shape: BoxShape.circle,
          ),
        ),

        // 標題樣式
        headerStyle: const HeaderStyle(
          formatButtonVisible: true,
          titleCentered: true,
          formatButtonShowsNext: false,
          formatButtonDecoration: BoxDecoration(
            color: AppColors.royalIndigo,
            borderRadius: BorderRadius.all(Radius.circular(12.0)),
          ),
          formatButtonTextStyle: TextStyle(
            color: Colors.white,
          ),
        ),

        // 自定義標題構建器
        calendarBuilders: CalendarBuilders<AstroEvent>(
          // 自定義標題
          headerTitleBuilder: (context, day) {
            return GestureDetector(
              onTap: () => _showDatePicker(viewModel),
              child: Container(
                padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
                decoration: BoxDecoration(
                  color: AppColors.royalIndigo.withOpacity(0.1),
                  borderRadius: BorderRadius.circular(6),
                  border: Border.all(color: AppColors.royalIndigo.withOpacity(0.3)),
                ),
                child: IntrinsicWidth(
                  child: Row(
                    mainAxisSize: MainAxisSize.min,
                    children: [
                      Flexible(
                        child: Text(
                          '${day.year}年${day.month}月',
                          style: const TextStyle(
                            fontSize: 14,
                            fontWeight: FontWeight.bold,
                            color: AppColors.royalIndigo,
                          ),
                          overflow: TextOverflow.ellipsis,
                        ),
                      ),
                      const SizedBox(width: 2),
                      const Icon(
                        Icons.arrow_drop_down,
                        color: AppColors.royalIndigo,
                        size: 18,
                      ),
                    ],
                  ),
                ),
              ),
            );
          },
          // 自定義事件標記
          markerBuilder: (context, day, events) {
            if (events.isNotEmpty) {
              return AstroEventMarker(
                events: events,
                size: 6.0,
                showCount: true,
              );
            }
            return null;
          },
        ),

        // 事件回調
        onDaySelected: (selectedDay, focusedDay) {
          viewModel.setSelectedDay(selectedDay);
          viewModel.setFocusedDay(focusedDay);
        },

        onFormatChanged: (format) {
          viewModel.setCalendarFormat(format);
        },

        onPageChanged: (focusedDay) {
          viewModel.setFocusedDay(focusedDay);
        },
      ),

          // Loading覆蓋層
          if (viewModel.isLoading)
            Container(
              decoration: BoxDecoration(
                color: Colors.white.withOpacity(0.8),
                borderRadius: BorderRadius.circular(8),
              ),
              child: const Center(
                child: Padding(
                  padding: EdgeInsets.all(16.0),
                  child: Row(
                    mainAxisSize: MainAxisSize.min,
                    children: [
                      SizedBox(
                        width: 20,
                        height: 20,
                        child: CircularProgressIndicator(
                          strokeWidth: 2,
                          valueColor: AlwaysStoppedAnimation<Color>(
                            AppColors.royalIndigo,
                          ),
                        ),
                      ),
                      SizedBox(width: 12),
                      Text(
                        '載入中...',
                        style: TextStyle(
                          color: AppColors.royalIndigo,
                          fontWeight: FontWeight.w500,
                        ),
                      ),
                    ],
                  ),
                ),
              ),
            ),
        ],
      ),
    );
  }

  /// 構建事件詳情
  Widget _buildEventDetails(AstroCalendarViewModel viewModel) {
    if (viewModel.isLoading) {
      return _buildLoadingWidget();
    }

    return SingleChildScrollView(
      child: DailyEventDetail(
        selectedDate: viewModel.selectedDay,
        events: viewModel.selectedDayEvents,
        onTapEvent: (event) => _navigateToEventDetail(event, viewModel),
      ),
    );
  }

  /// 構建載入中的UI
  Widget _buildLoadingWidget() {
    return Center(
      child: Container(
        width: double.infinity,
        height: double.infinity,
        padding: const EdgeInsets.all(32),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          crossAxisAlignment: CrossAxisAlignment.center,
          children: [
            // 自定義載入動畫
            Stack(
              alignment: Alignment.center,
              children: [
                // 外圈 - 慢速旋轉
                SizedBox(
                  width: 100,
                  height: 100,
                  child: CircularProgressIndicator(
                    strokeWidth: 3,
                    valueColor: AlwaysStoppedAnimation<Color>(
                      AppColors.royalIndigo.withOpacity(0.3),
                    ),
                  ),
                ),
                // 內圈 - 快速旋轉
                SizedBox(
                  width: 60,
                  height: 60,
                  child: CircularProgressIndicator(
                    strokeWidth: 4,
                    valueColor: const AlwaysStoppedAnimation<Color>(
                      AppColors.royalIndigo,
                    ),
                  ),
                ),
                // 中心圖標
                Container(
                  width: 40,
                  height: 40,
                  decoration: BoxDecoration(
                    color: AppColors.solarAmber,
                    borderRadius: BorderRadius.circular(20),
                    boxShadow: [
                      BoxShadow(
                        color: AppColors.solarAmber.withOpacity(0.4),
                        blurRadius: 12,
                        offset: const Offset(0, 3),
                      ),
                    ],
                  ),
                  child: const Icon(
                    Icons.auto_awesome,
                    color: Colors.white,
                    size: 22,
                  ),
                ),
              ],
            ),

            const SizedBox(height: 36),

            // 載入文字
            const Text(
              '正在計算星象事件...',
              style: TextStyle(
                fontSize: 20,
                fontWeight: FontWeight.w600,
                color: AppColors.royalIndigo,
                letterSpacing: 0.5,
              ),
              textAlign: TextAlign.center,
            ),

            const SizedBox(height: 16),

            // 提示文字
            Text(
              '包含月相、節氣、相位、換座等事件',
              style: TextStyle(
                fontSize: 15,
                color: Colors.grey[600],
                height: 1.4,
              ),
              textAlign: TextAlign.center,
            ),

            const SizedBox(height: 32),

            // 計算進度提示
            Container(
              padding: const EdgeInsets.symmetric(horizontal: 20, vertical: 12),
              decoration: BoxDecoration(
                color: AppColors.royalIndigo.withOpacity(0.08),
                borderRadius: BorderRadius.circular(25),
                border: Border.all(
                  color: AppColors.royalIndigo.withOpacity(0.2),
                  width: 1,
                ),
              ),
              child: Row(
                mainAxisSize: MainAxisSize.min,
                children: [
                  SizedBox(
                    width: 18,
                    height: 18,
                    child: CircularProgressIndicator(
                      strokeWidth: 2.5,
                      valueColor: const AlwaysStoppedAnimation<Color>(
                        AppColors.royalIndigo,
                      ),
                    ),
                  ),
                  const SizedBox(width: 12),
                  Text(
                    '精密計算中',
                    style: TextStyle(
                      fontSize: 14,
                      color: AppColors.royalIndigo,
                      fontWeight: FontWeight.w600,
                    ),
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  /// 顯示日期選擇器
  void _showDatePicker(AstroCalendarViewModel viewModel) {
    DatePicker.DatePicker.showDatePicker(
      context,
      showTitleActions: true,
      minTime: DateTime(1800, 1, 1),
      maxTime: DateTime(3000, 12, 31),
      onConfirm: (selectedDate) async {
        // 確定按鈕的回調 - 添加loading狀態
        try {
          // 檢查是否跨月份（在設置新日期之前）
          final currentFocusedDay = viewModel.focusedDay;
          final isMonthChanged = selectedDate.year != currentFocusedDay.year ||
              selectedDate.month != currentFocusedDay.month;

          // 設置新的日期
          if (isMonthChanged) {
            // 跨月份：先設置日期（不自動載入），然後手動載入
            viewModel.setFocusedDay(selectedDate, autoLoad: false);
            viewModel.setSelectedDay(selectedDate);
            await viewModel.loadMonthlyEvents(selectedDate.year, selectedDate.month);
          } else {
            // 同月份內：正常設置日期，不需要重新載入
            viewModel.setFocusedDay(selectedDate, autoLoad: false);
            viewModel.setSelectedDay(selectedDate);
            viewModel.setLoading(false);
          }
        } catch (e) {
          // 錯誤處理
          viewModel.setLoading(false);
          if (mounted) {
            ScaffoldMessenger.of(context).showSnackBar(
              SnackBar(
                content: Text('載入日期事件失敗: $e'),
                backgroundColor: Colors.red,
              ),
            );
          }
        }
      },
      onCancel: () {
        // 取消按鈕的回調（可選）
        print('用戶取消了日期選擇');
      },
      currentTime: viewModel.focusedDay,
      locale: DatePicker.LocaleType.tw,
      theme: const DatePicker.DatePickerTheme(
        backgroundColor: Colors.white,
        headerColor: Colors.white,
        itemStyle: TextStyle(
          color: Colors.black87,
          fontSize: 18,
          fontWeight: FontWeight.w500,
        ),
        doneStyle: TextStyle(
          color: AppColors.royalIndigo,
          fontSize: 16,
          fontWeight: FontWeight.bold,
        ),
        cancelStyle: TextStyle(
          color: Colors.grey,
          fontSize: 16,
        ),
        titleHeight: 50.0,
        containerHeight: 240.0,
        itemHeight: 40.0,
      ),
    );
  }

  /// 顯示篩選對話框
  void _showFilterDialog() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('事件篩選'),
        content: SizedBox(
          width: double.maxFinite,
          child: EventTypeFilter(
            selectedTypes: _viewModel.selectedEventTypes,
            onToggle: _viewModel.toggleEventType,
            getDisplayName: _viewModel.getEventTypeDisplayName,
            getIcon: _viewModel.getEventTypeIcon,
            getColor: _viewModel.getEventTypeColor,
          ),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('關閉'),
          ),
        ],
      ),
    );
  }

  /// 導航到事件詳情頁面
  void _navigateToEventDetail(AstroEvent event, AstroCalendarViewModel viewModel) {
    Navigator.push(
      context,
      MaterialPageRoute(
        builder: (context) => AstroEventDetailPage(
          event: event,
          natalPerson: viewModel.natalPerson,
          latitude: viewModel.latitude,
          longitude: viewModel.longitude,
        ),
      ),
    );
  }
}
