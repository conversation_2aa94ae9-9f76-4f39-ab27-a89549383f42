import 'package:flutter/material.dart';
import 'package:provider/provider.dart';

import '../../models/astro_event.dart';
import '../../models/birth_data.dart';
import '../../models/chart_data.dart';
import '../../models/chart_type.dart';
import '../../ui/AppTheme.dart';
import '../../viewmodels/chart_viewmodel.dart';
import '../../widgets/styled_card.dart';
import 'chart_page.dart';

/// 星象事件詳情頁面
class AstroEventDetailPage extends StatelessWidget {
  final AstroEvent event;
  final BirthData? natalPerson;
  final double latitude;
  final double longitude;

  const AstroEventDetailPage({
    super.key,
    required this.event,
    this.natalPerson,
    this.latitude = 25.0,
    this.longitude = 121.0,
  });

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text(event.title),
        backgroundColor: AppColors.royalIndigo,
        foregroundColor: Colors.white,
      ),
      body: SingleChildScrollView(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // 事件基本信息卡片
            _buildEventInfoCard(),
            const SizedBox(height: 16),

            // 事件詳細描述卡片
            _buildEventDescriptionCard(),
            const SizedBox(height: 16),

            // 星盤查看選項卡片
            _buildChartOptionsCard(context),
            const SizedBox(height: 16),

            // 如果有本命盤人物，顯示個人化影響
            if (natalPerson != null) ...[
              _buildPersonalImpactCard(),
              const SizedBox(height: 16),
            ],

            // 相關建議卡片
            _buildAdviceCard(),
          ],
        ),
      ),
    );
  }

  /// 構建事件基本信息卡片
  Widget _buildEventInfoCard() {
    return StyledCard(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // 事件標題和圖標
            Row(
              children: [
                Container(
                  padding: const EdgeInsets.all(8),
                  decoration: BoxDecoration(
                    color: event.color,
                    shape: BoxShape.circle,
                  ),
                  child: Icon(
                    event.icon,
                    color: Colors.white,
                    size: 24,
                  ),
                ),
                const SizedBox(width: 12),
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        event.title,
                        style: const TextStyle(
                          fontSize: 18,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                      Text(
                        event.typeDisplayName,
                        style: TextStyle(
                          fontSize: 14,
                          color: Colors.grey[600],
                        ),
                      ),
                    ],
                  ),
                ),
                // 重要度指示器
                Container(
                  padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                  decoration: BoxDecoration(
                    color: event.importanceColor,
                    borderRadius: BorderRadius.circular(12),
                  ),
                  child: Row(
                    mainAxisSize: MainAxisSize.min,
                    children: [
                      const Icon(
                        Icons.star,
                        color: Colors.white,
                        size: 16,
                      ),
                      const SizedBox(width: 4),
                      Text(
                        '${event.importance}',
                        style: const TextStyle(
                          color: Colors.white,
                          fontSize: 12,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                    ],
                  ),
                ),
              ],
            ),
            const SizedBox(height: 16),

            // 時間信息
            _buildInfoRow(
              Icons.access_time,
              '發生時間',
              _formatDateTime(event.dateTime),
            ),
            const SizedBox(height: 8),

            // 事件類型
            _buildInfoRow(
              Icons.category,
              '事件類型',
              event.typeDisplayName,
            ),

            // 如果有額外數據，顯示相關信息
            if (event.additionalData != null) ...[
              const SizedBox(height: 8),
              ..._buildAdditionalInfo(),
            ],
          ],
        ),
      ),
    );
  }

  /// 構建事件描述卡片
  Widget _buildEventDescriptionCard() {
    return StyledCard(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Text(
              '事件描述',
              style: TextStyle(
                fontSize: 16,
                fontWeight: FontWeight.bold,
                color: AppColors.royalIndigo,
              ),
            ),
            const SizedBox(height: 8),
            Text(
              event.description,
              style: const TextStyle(fontSize: 14),
            ),
            const SizedBox(height: 12),
            Text(
              _getEventExplanation(),
              style: TextStyle(
                fontSize: 13,
                color: Colors.grey[600],
              ),
            ),
          ],
        ),
      ),
    );
  }

  /// 構建星盤查看選項卡片
  Widget _buildChartOptionsCard(BuildContext context) {
    return StyledCard(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Text(
              '查看星盤',
              style: TextStyle(
                fontSize: 16,
                fontWeight: FontWeight.bold,
                color: AppColors.royalIndigo,
              ),
            ),
            const SizedBox(height: 12),

            // 查看事件時刻星盤
            _buildChartOptionButton(
              context,
              icon: Icons.public,
              title: '事件時刻星盤',
              subtitle: '查看事件發生時的天象配置',
              onTap: () => _navigateToEventChart(context),
            ),

            // 如果有本命盤人物，顯示行運盤選項
            if (natalPerson != null) ...[
              const SizedBox(height: 8),
              _buildChartOptionButton(
                context,
                icon: Icons.person,
                title: '個人行運盤',
                subtitle: '查看事件對${natalPerson!.name}的影響',
                onTap: () => _navigateToTransitChart(context),
              ),
            ],
          ],
        ),
      ),
    );
  }

  /// 構建個人化影響卡片
  Widget _buildPersonalImpactCard() {
    return StyledCard(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              '對${natalPerson!.name}的影響',
              style: const TextStyle(
                fontSize: 16,
                fontWeight: FontWeight.bold,
                color: AppColors.royalIndigo,
              ),
            ),
            const SizedBox(height: 8),
            Text(
              _getPersonalImpact(),
              style: const TextStyle(fontSize: 14),
            ),
          ],
        ),
      ),
    );
  }

  /// 構建建議卡片
  Widget _buildAdviceCard() {
    return StyledCard(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Text(
              '相關建議',
              style: TextStyle(
                fontSize: 16,
                fontWeight: FontWeight.bold,
                color: AppColors.royalIndigo,
              ),
            ),
            const SizedBox(height: 8),
            Text(
              _getAdvice(),
              style: const TextStyle(fontSize: 14),
            ),
          ],
        ),
      ),
    );
  }

  /// 構建信息行
  Widget _buildInfoRow(IconData icon, String label, String value) {
    return Row(
      children: [
        Icon(icon, size: 16, color: Colors.grey[600]),
        const SizedBox(width: 8),
        Text(
          '$label: ',
          style: TextStyle(
            fontSize: 14,
            color: Colors.grey[600],
          ),
        ),
        Expanded(
          child: Text(
            value,
            style: const TextStyle(
              fontSize: 14,
              fontWeight: FontWeight.w500,
            ),
          ),
        ),
      ],
    );
  }

  /// 構建星盤選項按鈕
  Widget _buildChartOptionButton(
    BuildContext context, {
    required IconData icon,
    required String title,
    required String subtitle,
    required VoidCallback onTap,
  }) {
    return InkWell(
      onTap: onTap,
      borderRadius: BorderRadius.circular(8),
      child: Container(
        padding: const EdgeInsets.all(12),
        decoration: BoxDecoration(
          border: Border.all(color: Colors.grey[300]!),
          borderRadius: BorderRadius.circular(8),
        ),
        child: Row(
          children: [
            Icon(icon, color: AppColors.royalIndigo),
            const SizedBox(width: 12),
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    title,
                    style: const TextStyle(
                      fontSize: 14,
                      fontWeight: FontWeight.w500,
                    ),
                  ),
                  Text(
                    subtitle,
                    style: TextStyle(
                      fontSize: 12,
                      color: Colors.grey[600],
                    ),
                  ),
                ],
              ),
            ),
            Icon(Icons.arrow_forward_ios, size: 16, color: Colors.grey[400]),
          ],
        ),
      ),
    );
  }

  /// 構建額外信息
  List<Widget> _buildAdditionalInfo() {
    final additionalData = event.additionalData!;
    final widgets = <Widget>[];

    // 月相信息
    if (additionalData.containsKey('moonPhase')) {
      final moonPhase = additionalData['moonPhase'] as MoonPhaseType;
      widgets.add(_buildInfoRow(
        Icons.brightness_4,
        '月相類型',
        AstroEvent.getMoonPhaseDisplayName(moonPhase),
      ));
    }

    // 節氣信息
    if (additionalData.containsKey('seasonType')) {
      widgets.add(_buildInfoRow(
        Icons.wb_sunny,
        '節氣類型',
        '二分二至節氣',
      ));
    }

    // 相位信息
    if (additionalData.containsKey('planet1') && additionalData.containsKey('planet2')) {
      widgets.add(_buildInfoRow(
        Icons.timeline,
        '相位行星',
        '${additionalData['planet1']} - ${additionalData['planet2']}',
      ));
    }

    // 換座信息
    if (additionalData.containsKey('fromSign') && additionalData.containsKey('toSign')) {
      widgets.add(_buildInfoRow(
        Icons.swap_horiz,
        '星座變化',
        '${additionalData['fromSign']} → ${additionalData['toSign']}',
      ));
    }

    return widgets;
  }

  /// 格式化日期時間
  String _formatDateTime(DateTime dateTime) {
    return '${dateTime.year}年${dateTime.month}月${dateTime.day}日 '
        '${dateTime.hour.toString().padLeft(2, '0')}:'
        '${dateTime.minute.toString().padLeft(2, '0')}';
  }

  /// 獲取事件解釋
  String _getEventExplanation() {
    switch (event.type) {
      case AstroEventType.moonPhase:
        return '月相變化代表情緒和直覺能量的轉換，影響人們的內在感受和潛意識活動。';
      case AstroEventType.seasonChange:
        return '節氣變化標誌著太陽能量的重要轉折點，對自然界和人類活動都有深遠影響。';
      case AstroEventType.planetAspect:
        return '行星相位代表不同天體能量之間的互動關係，影響個人和集體的運勢發展。';
      case AstroEventType.planetSignChange:
        return '行星換座表示行星能量進入新的表達方式，為相關領域帶來新的影響和機會。';
      case AstroEventType.planetRetrograde:
        return '行星逆行期間，該行星所代表的領域可能出現回顧、重新評估或延遲的情況。';
      case AstroEventType.eclipse:
        return '日月食是強力的轉化時期，往往帶來重要的開始或結束，影響深遠。';
    }
  }

  /// 獲取個人化影響
  String _getPersonalImpact() {
    if (natalPerson == null) return '';

    switch (event.type) {
      case AstroEventType.moonPhase:
        return '月相變化可能影響您的情緒狀態和直覺感受，建議關注內在需求和感受變化。';
      case AstroEventType.seasonChange:
        return '節氣變化為您帶來新的能量週期，適合調整生活節奏和目標方向。';
      case AstroEventType.planetAspect:
        return '行星相位可能在相關生活領域帶來新的機會或挑戰，建議保持開放和靈活的態度。';
      case AstroEventType.planetSignChange:
        return '行星換座可能為您的相關生活領域帶來新的發展方向和表達方式。';
      case AstroEventType.planetRetrograde:
        return '逆行期間建議回顧和整理相關領域的事務，避免重大決策。';
      case AstroEventType.eclipse:
        return '食相期間可能為您的人生帶來重要轉折，建議保持覺察和準備迎接變化。';
    }
  }

  /// 獲取建議
  String _getAdvice() {
    switch (event.type) {
      case AstroEventType.moonPhase:
        return '建議：關注情緒變化，進行冥想或反思活動，注意休息和情緒調節。';
      case AstroEventType.seasonChange:
        return '建議：調整作息時間，配合自然節律，制定新的計劃和目標。';
      case AstroEventType.planetAspect:
        return '建議：保持開放心態，善用行星能量的互動，在相關領域積極行動。';
      case AstroEventType.planetSignChange:
        return '建議：關注新的發展機會，調整策略和方法，適應新的能量表達。';
      case AstroEventType.planetRetrograde:
        return '建議：回顧過往經驗，整理相關事務，避免簽署重要合約或做重大決定。';
      case AstroEventType.eclipse:
        return '建議：保持覺察和彈性，準備迎接重要變化，關注新的開始或結束。';
    }
  }

  /// 導航到事件時刻星盤
  void _navigateToEventChart(BuildContext context) {
    // 創建事件時刻的虛擬人物數據
    final eventPerson = BirthData(
      id: 'event_${event.id}',
      name: event.title,
      birthDate: event.dateTime,
      latitude: latitude,
      longitude: longitude,
      birthPlace: '事件發生地',
    );

    // 創建星盤數據
    final chartData = ChartData(
      chartType: ChartType.event,
      primaryPerson: eventPerson,
    );

    // 導航到星盤頁面
    Navigator.push(
      context,
      MaterialPageRoute(
        builder: (context) => ChangeNotifierProvider(
          create: (_) => ChartViewModel.withChartData(initialChartData: chartData),
          child: ChartPage(chartData: chartData),
        ),
      ),
    );
  }

  /// 導航到行運盤
  void _navigateToTransitChart(BuildContext context) {
    if (natalPerson == null) return;

    // 創建行運盤數據
    final chartData = ChartData(
      chartType: ChartType.transit,
      primaryPerson: natalPerson!,
      specificDate: event.dateTime,
    );

    // 導航到星盤頁面
    Navigator.push(
      context,
      MaterialPageRoute(
        builder: (context) => ChangeNotifierProvider(
          create: (_) => ChartViewModel.withChartData(initialChartData: chartData),
          child: ChartPage(chartData: chartData),
        ),
      ),
    );
  }
}
