基於我對現有代碼的分析，我來為您規劃星象日曆功能。讓我先了解一下您希望這個功能具體包含哪些內容。
星象日曆功能規劃
根據現有的代碼結構和您的偏好，我建議以下規劃：
🎯 核心功能目標
月曆視圖：顯示每月的重要天文事件
日期詳情：點擊日期查看當日星象詳情
個人化提醒：結合用戶本命盤的個人化星象提醒
事件篩選：可選擇顯示的星象事件類型
📋 詳細功能規劃
1. 主要UI組件
   月曆視圖：使用Flutter的日曆套件顯示月份
   事件標記：在日期上標記重要星象事件
   日期詳情頁：點擊日期進入詳細星象資訊
   篩選器：選擇要顯示的事件類型
2. 星象事件類型
   行星相位：重要的行星相位（合相、對分、四分等）
   行星換座：行星進入新星座
   月相變化：新月、滿月、上弦月、下弦月
   行星逆行：水逆、金逆、火逆等開始/結束
   節氣變化：二分二至及其他節氣
   日月食：日食、月食事件
3. 個人化功能
   本命盤關聯：顯示行運對個人星盤的影響
   重要度評級：根據對個人的影響程度標記事件重要性
   提醒設置：可設置重要事件的提前提醒
4. 技術實現要點
   需要的新依賴項目：
   table_calendar: ^3.0.9  # 日曆組件
   flutter_local_notifications: ^17.2.3  # 本地通知
   主要文件結構：
   lib/
   ├── ui/pages/
   │   └── astro_calendar_page.dart          # 星象日曆主頁面
   ├── widgets/
   │   ├── calendar_widget.dart              # 日曆組件
   │   ├── astro_event_marker.dart           # 星象事件標記
   │   └── daily_astro_detail_widget.dart    # 日期詳情組件
   ├── models/
   │   └── astro_event.dart                  # 星象事件模型
   ├── services/

🔄 與現有功能的整合
復用現有計算邏輯：利用現有的AstrologyCalculator和EquinoxSolsticeService
整合今日星相：將首頁的今日星相功能整合到日曆中
連接星盤頁面：點擊事件可直接進入對應的星盤頁面
使用現有UI風格：遵循現有的卡片式設計和顏色主題
📱 UI設計原則
緊湊布局：符合您偏好的一行顯示原則
卡片式設計：使用現有的StyledCard組件
顏色編碼：不同類型事件使用不同顏色標記
響應式設計：適應不同螢幕尺寸
🚀 實施步驟建議
第一階段：基礎日曆UI和月相/節氣顯示
第二階段：行星相位和換座事件
第三階段：個人化功能和提醒
第四階段：進階篩選和統計功能
您希望我開始實施這個規劃嗎？我建議先從第一階段開始，創建基礎的日曆UI和整合現有的節氣功能。您對這個規劃有什麼想法或需要調整的地方嗎？

－－

好的！我將開始實施星象日曆功能的第一和第二階段。讓我先添加必要的依賴項目，然後創建基礎的日曆UI和相關功能。

## 第一步：添加依賴項目

## 第二步：創建星象事件模型

我發現模型文件中有個語法錯誤，讓我修正它：

## 第三步：創建星象日曆服務

現在讓我繼續添加行星相位和換座事件的計算方法：

## 第四步：創建星象日曆ViewModel

## 第五步：創建日曆UI組件

## 第六步：創建星象日曆主頁面

## 第七步：將星象日曆頁面添加到導航中

首先，讓我們將新頁面添加到pages.dart中：

現在讓我們在分析頁面中添加星象日曆的入口：

現在讓我們添加星象日曆部分的構建方法：

現在我們需要修改`_buildAnalysisCard`方法來支持星象日曆的導航。讓我先查看現有的方法：

現在我需要修改`_buildAnalysisCard`方法來支持星象日曆的導航：

現在我們需要添加必要的導入語句：

## 第八步：安裝新的依賴項目

現在讓我們安裝新添加的依賴項目：

我看到依賴項目有衝突。讓我修正這個問題，使用兼容的版本：

現在重新安裝依賴項目：

很好！依賴項目安裝成功。現在讓我們測試一下應用是否能正常編譯：

我看到有一些錯誤需要修正。讓我修正這些問題：

1. 首先修正缺少的模型導入：

2. 修正AspectInfo的屬性名稱：

現在我知道了AspectInfo模型使用的是`aspect`屬性而不是`aspectType`。讓我修正這個問題：

3. 修正相位重要度方法中的相位名稱：

4. 修正相位顏色方法中的相位名稱：

5. 移除未使用的導入：

現在讓我們再次檢查編譯狀況：

很好！現在只有一些警告，沒有錯誤了。讓我們測試一下應用是否能正常運行：

我看到有Android配置問題。讓我們先修正這些問題。首先修正NDK版本：

讓我修正Android配置：

現在我需要添加core library desugaring依賴：

現在讓我們再次嘗試編譯：

現在讓我們測試一下基本的編譯：

太好了！現在讓我們創建一個簡單的測試來驗證功能：

現在讓我們運行測試來驗證功能：
