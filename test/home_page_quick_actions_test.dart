import 'package:astreal/ui/pages/main/home_page.dart';
import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';

void main() {
  group('首頁快捷按鈕測試', () {
    testWidgets('快捷功能按鈕顯示測試', (WidgetTester tester) async {
      // 構建首頁
      await tester.pumpWidget(
        const MaterialApp(
          home: HomePage(),
        ),
      );

      // 等待頁面渲染完成
      await tester.pumpAndSettle();

      // 驗證快捷功能標題
      expect(find.text('快捷功能'), findsOneWidget);

      // 驗證卜卦分析按鈕
      expect(find.text('卜卦分析'), findsOneWidget);
      expect(find.text('占星、周易、塔羅'), findsOneWidget);

      // 驗證星象日曆按鈕
      expect(find.text('星象日曆'), findsOneWidget);
      expect(find.text('月相、節氣、相位'), findsOneWidget);

      // 驗證按鈕圖標
      expect(find.byIcon(Icons.auto_awesome), findsOneWidget);
      expect(find.byIcon(Icons.calendar_month), findsOneWidget);
    });

    testWidgets('快捷按鈕點擊測試', (WidgetTester tester) async {
      // 構建首頁
      await tester.pumpWidget(
        const MaterialApp(
          home: HomePage(),
        ),
      );

      // 等待頁面渲染完成
      await tester.pumpAndSettle();

      // 測試卜卦分析按鈕點擊
      final divinationButton = find.text('卜卦分析');
      expect(divinationButton, findsOneWidget);
      
      // 點擊卜卦分析按鈕
      await tester.tap(divinationButton);
      await tester.pumpAndSettle();

      // 驗證是否導航到卜卦分析頁面
      // 注意：在測試環境中，我們主要驗證按鈕是否可點擊
      // 實際的頁面導航在集成測試中驗證
    });

    testWidgets('快捷按鈕佈局測試', (WidgetTester tester) async {
      // 構建首頁
      await tester.pumpWidget(
        const MaterialApp(
          home: HomePage(),
        ),
      );

      // 等待頁面渲染完成
      await tester.pumpAndSettle();

      // 驗證快捷按鈕是否在同一行
      final quickActionButtons = find.byType(InkWell);
      expect(quickActionButtons.evaluate().length, greaterThanOrEqualTo(2));

      // 驗證按鈕容器的存在
      final containers = find.byType(Container);
      expect(containers, findsWidgets);

      // 驗證圓形圖標容器
      final iconContainers = find.descendant(
        of: find.byType(InkWell),
        matching: find.byType(Container),
      );
      expect(iconContainers, findsWidgets);
    });

    testWidgets('快捷按鈕樣式測試', (WidgetTester tester) async {
      // 構建首頁
      await tester.pumpWidget(
        const MaterialApp(
          home: HomePage(),
        ),
      );

      // 等待頁面渲染完成
      await tester.pumpAndSettle();

      // 查找卜卦分析按鈕的文本樣式
      final divinationTitle = find.text('卜卦分析');
      expect(divinationTitle, findsOneWidget);

      // 查找星象日曆按鈕的文本樣式
      final calendarTitle = find.text('星象日曆');
      expect(calendarTitle, findsOneWidget);

      // 驗證副標題文本
      final divinationSubtitle = find.text('占星、周易、塔羅');
      expect(divinationSubtitle, findsOneWidget);

      final calendarSubtitle = find.text('月相、節氣、相位');
      expect(calendarSubtitle, findsOneWidget);
    });

    test('快捷按鈕配置測試', () {
      // 測試按鈕配置的正確性
      const divinationIcon = Icons.auto_awesome;
      const calendarIcon = Icons.calendar_month;

      // 驗證圖標常量
      expect(divinationIcon, equals(Icons.auto_awesome));
      expect(calendarIcon, equals(Icons.calendar_month));

      // 驗證文本內容
      const divinationTitle = '卜卦分析';
      const divinationSubtitle = '占星、周易、塔羅';
      const calendarTitle = '星象日曆';
      const calendarSubtitle = '月相、節氣、相位';

      expect(divinationTitle, isNotEmpty);
      expect(divinationSubtitle, isNotEmpty);
      expect(calendarTitle, isNotEmpty);
      expect(calendarSubtitle, isNotEmpty);
    });
  });
}
