import 'package:astreal/models/astro_event.dart';
import 'package:astreal/models/birth_data.dart';
import 'package:astreal/ui/pages/astro_event_detail_page.dart';
import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';

void main() {
  group('星象事件詳情頁面測試', () {
    late AstroEvent testEvent;
    late BirthData testPerson;

    setUp(() {
      // 創建測試用的星象事件
      testEvent = AstroEvent(
        id: 'test_event_1',
        title: '滿月',
        description: '月相變化：滿月',
        dateTime: DateTime(2024, 1, 15, 14, 30),
        type: AstroEventType.moonPhase,
        color: Colors.indigo,
        icon: Icons.brightness_7,
        importance: 4,
        additionalData: const {
          'moonPhase': MoonPhaseType.fullMoon,
        },
      );

      // 創建測試用的人物數據
      testPerson = BirthData(
        id: 'test_person_1',
        name: '測試人物',
        birthDate: DateTime(1990, 5, 15, 10, 30),
        latitude: 25.0,
        longitude: 121.0,
        birthPlace: '台北',
      );
    });

    testWidgets('事件詳情頁面基本顯示測試', (WidgetTester tester) async {
      // 構建事件詳情頁面
      await tester.pumpWidget(
        MaterialApp(
          home: AstroEventDetailPage(
            event: testEvent,
            natalPerson: testPerson,
            latitude: 25.0,
            longitude: 121.0,
          ),
        ),
      );

      // 等待頁面渲染完成
      await tester.pumpAndSettle();

      // 驗證頁面標題
      expect(find.text('滿月'), findsWidgets);

      // 驗證事件基本信息
      expect(find.text('月相變化：滿月'), findsOneWidget);
      expect(find.textContaining('月相'), findsWidgets);

      // 驗證時間顯示
      expect(find.textContaining('2024年1月15日'), findsOneWidget);
      expect(find.textContaining('14:30'), findsOneWidget);

      // 驗證重要度顯示
      expect(find.textContaining('4'), findsWidgets);

      // 驗證星盤查看選項
      expect(find.text('查看星盤'), findsOneWidget);
      expect(find.text('事件時刻星盤'), findsOneWidget);
      expect(find.text('個人行運盤'), findsOneWidget);

      // 驗證個人化影響部分
      expect(find.textContaining('對測試人物的影響'), findsWidgets);

      // 驗證建議部分
      expect(find.text('相關建議'), findsOneWidget);
    });

    testWidgets('不同事件類型的詳情顯示測試', (WidgetTester tester) async {
      // 測試行星相位事件
      final aspectEvent = AstroEvent(
        id: 'test_aspect_1',
        title: '太陽合相月亮',
        description: '太陽與月亮形成合相相位',
        dateTime: DateTime(2024, 2, 10, 8, 15),
        type: AstroEventType.planetAspect,
        color: Colors.red,
        icon: Icons.timeline,
        importance: 5,
        additionalData: const {
          'planet1': '太陽',
          'planet2': '月亮',
          'aspectType': '合相',
        },
      );

      await tester.pumpWidget(
        MaterialApp(
          home: AstroEventDetailPage(
            event: aspectEvent,
            latitude: 25.0,
            longitude: 121.0,
          ),
        ),
      );

      await tester.pumpAndSettle();

      // 驗證相位事件特有信息
      expect(find.text('太陽合相月亮'), findsWidgets);
      expect(find.textContaining('相位'), findsWidgets);
      expect(find.textContaining('太陽 - 月亮'), findsOneWidget);

      // 驗證沒有個人化影響部分（因為沒有提供natalPerson）
      expect(find.textContaining('的影響'), findsNothing);
    });

    testWidgets('行星換座事件詳情顯示測試', (WidgetTester tester) async {
      // 測試行星換座事件
      final signChangeEvent = AstroEvent(
        id: 'test_sign_change_1',
        title: '火星進入白羊座',
        description: '火星從雙魚座進入白羊座',
        dateTime: DateTime(2024, 3, 20, 12, 0),
        type: AstroEventType.planetSignChange,
        color: Colors.blue,
        icon: Icons.swap_horiz,
        importance: 3,
        additionalData: const {
          'planet': '火星',
          'fromSign': '雙魚座',
          'toSign': '白羊座',
        },
      );

      await tester.pumpWidget(
        MaterialApp(
          home: AstroEventDetailPage(
            event: signChangeEvent,
            natalPerson: testPerson,
            latitude: 25.0,
            longitude: 121.0,
          ),
        ),
      );

      await tester.pumpAndSettle();

      // 驗證換座事件特有信息
      expect(find.text('火星進入白羊座'), findsWidgets);
      expect(find.textContaining('換座'), findsWidgets);
      expect(find.textContaining('雙魚座 → 白羊座'), findsOneWidget);
    });

    test('事件解釋文本測試', () {
      // 創建頁面實例來測試私有方法的邏輯
      final page = AstroEventDetailPage(
        event: AstroEvent(
          id: 'test',
          title: 'Test',
          description: 'Test Description',
          dateTime: DateTime(2024, 1, 1),
          type: AstroEventType.moonPhase,
          color: Colors.blue,
          icon: Icons.star,
        ),
      );

      // 驗證不同事件類型都有對應的解釋
      for (final eventType in AstroEventType.values) {
        final testEvent = AstroEvent(
          id: 'test_${eventType.name}',
          title: 'Test ${eventType.name}',
          description: 'Test Description',
          dateTime: DateTime(2024, 1, 1),
          type: eventType,
          color: Colors.blue,
          icon: Icons.star,
        );

        // 這裡我們無法直接測試私有方法，但可以確保每種類型都有對應的處理
        expect(testEvent.type, equals(eventType));
        expect(testEvent.typeDisplayName, isNotEmpty);
      }
    });
  });
}
