import 'package:astreal/models/astro_event.dart';
import 'package:astreal/viewmodels/astro_calendar_viewmodel.dart';
import 'package:astreal/widgets/astro_event_marker.dart';
import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';

/// 測試事件篩選功能
void main() {
  group('事件篩選功能測試', () {
    late AstroCalendarViewModel viewModel;

    setUp(() {
      viewModel = AstroCalendarViewModel();
    });

    test('測試 ViewModel 篩選狀態初始化', () {
      // 驗證初始狀態：所有事件類型都被選中
      expect(viewModel.selectedEventTypes.length, equals(AstroEventType.values.length));

      for (final type in AstroEventType.values) {
        expect(viewModel.selectedEventTypes.contains(type), isTrue);
      }
    });

    test('測試 toggleEventType 方法', () {
      // 測試取消選擇
      viewModel.toggleEventType(AstroEventType.moonPhase);
      expect(viewModel.selectedEventTypes.contains(AstroEventType.moonPhase), isFalse);
      expect(viewModel.selectedEventTypes.length, equals(AstroEventType.values.length - 1));

      // 測試重新選擇
      viewModel.toggleEventType(AstroEventType.moonPhase);
      expect(viewModel.selectedEventTypes.contains(AstroEventType.moonPhase), isTrue);
      expect(viewModel.selectedEventTypes.length, equals(AstroEventType.values.length));
    });

    test('測試事件篩選邏輯', () {
      // 創建測試事件
      final testEvents = [
        AstroEvent(
          id: '1',
          title: '滿月',
          description: '月相事件',
          dateTime: DateTime(2024, 1, 15),
          type: AstroEventType.moonPhase,
          color: Colors.blue,
          icon: Icons.brightness_7,
        ),
        AstroEvent(
          id: '2',
          title: '日全蝕',
          description: '日蝕事件',
          dateTime: DateTime(2024, 1, 15),
          type: AstroEventType.eclipse,
          color: Colors.black,
          icon: Icons.wb_sunny_outlined,
        ),
        AstroEvent(
          id: '3',
          title: '水星逆行',
          description: '逆行事件',
          dateTime: DateTime(2024, 1, 15),
          type: AstroEventType.planetRetrograde,
          color: Colors.red,
          icon: Icons.refresh,
        ),
      ];

      // 模擬事件數據
      final testDate = DateTime(2024, 1, 15);
      viewModel.setEvents({testDate: testEvents});

      // 測試所有事件都被包含（初始狀態）
      final allEvents = viewModel.getEventsForDay(testDate);
      expect(allEvents.length, equals(3));

      // 測試篩選掉月相事件
      viewModel.toggleEventType(AstroEventType.moonPhase);
      final filteredEvents1 = viewModel.getEventsForDay(testDate);
      expect(filteredEvents1.length, equals(2));
      expect(filteredEvents1.any((e) => e.type == AstroEventType.moonPhase), isFalse);

      // 測試篩選掉日月蝕事件
      viewModel.toggleEventType(AstroEventType.eclipse);
      final filteredEvents2 = viewModel.getEventsForDay(testDate);
      expect(filteredEvents2.length, equals(1));
      expect(filteredEvents2.first.type, equals(AstroEventType.planetRetrograde));

      // 測試全部篩選掉
      viewModel.toggleEventType(AstroEventType.planetRetrograde);
      final filteredEvents3 = viewModel.getEventsForDay(testDate);
      expect(filteredEvents3.length, equals(0));
    });

    testWidgets('測試 EventTypeFilter 組件', (WidgetTester tester) async {
      // 創建一個簡單的測試環境
      await tester.pumpWidget(
        MaterialApp(
          home: Scaffold(
            body: EventTypeFilter(
              selectedTypes: viewModel.selectedEventTypes,
              onToggle: viewModel.toggleEventType,
              getDisplayName: viewModel.getEventTypeDisplayName,
              getIcon: viewModel.getEventTypeIcon,
              getColor: viewModel.getEventTypeColor,
            ),
          ),
        ),
      );

      // 驗證所有事件類型都顯示
      for (final type in AstroEventType.values) {
        final displayName = viewModel.getEventTypeDisplayName(type);
        expect(find.text(displayName), findsOneWidget);
      }
    });

    test('測試事件類型顯示名稱', () {
      final expectedNames = {
        AstroEventType.moonPhase: '月相',
        AstroEventType.seasonChange: '節氣',
        AstroEventType.planetAspect: '相位',
        AstroEventType.planetSignChange: '換座',
        AstroEventType.planetRetrograde: '逆行',
        AstroEventType.eclipse: '蝕相',
      };

      for (final entry in expectedNames.entries) {
        expect(viewModel.getEventTypeDisplayName(entry.key), equals(entry.value));
      }
    });

    test('測試事件類型圖標', () {
      final expectedIcons = {
        AstroEventType.moonPhase: Icons.brightness_4,
        AstroEventType.seasonChange: Icons.wb_sunny,
        AstroEventType.planetAspect: Icons.timeline,
        AstroEventType.planetSignChange: Icons.swap_horiz,
        AstroEventType.planetRetrograde: Icons.refresh,
        AstroEventType.eclipse: Icons.brightness_2,
      };

      for (final entry in expectedIcons.entries) {
        expect(viewModel.getEventTypeIcon(entry.key), equals(entry.value));
      }
    });

    test('測試事件類型顏色', () {
      final expectedColors = {
        AstroEventType.moonPhase: Colors.indigo,
        AstroEventType.seasonChange: Colors.green,
        AstroEventType.planetAspect: Colors.orange,
        AstroEventType.planetSignChange: Colors.blue,
        AstroEventType.planetRetrograde: Colors.red,
        AstroEventType.eclipse: Colors.purple,
      };

      for (final entry in expectedColors.entries) {
        expect(viewModel.getEventTypeColor(entry.key), equals(entry.value));
      }
    });
  });
}
