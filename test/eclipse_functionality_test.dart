import 'package:flutter_test/flutter_test.dart';
import 'package:astreal/services/astro_calendar_service.dart';
import 'package:astreal/models/astro_event.dart';

/// 測試日月食功能
void main() {
  group('日月食功能測試', () {
    late AstroCalendarService calendarService;

    setUp(() {
      calendarService = AstroCalendarService();
    });

    testWidgets('測試2024年4月的星象事件（包含日食）', (WidgetTester tester) async {
      print('🌙 測試2024年4月的星象事件');

      try {
        final events = await calendarService.getMonthlyEvents(
          2024,
          4,
          latitude: 25.0, // 台北緯度
          longitude: 121.0, // 台北經度
        );

        print('總共找到 ${events.length} 個星象事件');

        // 篩選日月食事件
        final eclipseEvents = events.where((event) =>
          event.type == AstroEventType.eclipse
        ).toList();

        print('找到 ${eclipseEvents.length} 個日月食事件');

        // 驗證日月食事件的基本屬性
        for (final event in eclipseEvents) {
          expect(event.type, equals(AstroEventType.eclipse));
          expect(event.title, isNotEmpty);
          expect(event.description, isNotEmpty);
          expect(event.importance, greaterThan(0));
          expect(event.importance, lessThanOrEqualTo(5));

          print('  • ${event.title} - ${event.dateTime.toString().split(' ')[0]}');

          // 檢查額外資料
          if (event.additionalData != null) {
            final eclipseType = event.additionalData!['eclipseType'] as EclipseType?;
            final isVisible = event.additionalData!['isVisible'] as bool?;
            final magnitude = event.additionalData!['magnitude'] as double?;

            if (eclipseType != null) {
              expect(EclipseType.values, contains(eclipseType));
              print('    類型：${AstroEvent.getEclipseDisplayName(eclipseType)}');
            }

            if (isVisible != null) {
              expect(isVisible, isA<bool>());
              print('    可見性：${isVisible ? '可見' : '不可見'}');
            }

            if (magnitude != null) {
              expect(magnitude, greaterThanOrEqualTo(0.0));
              expect(magnitude, lessThanOrEqualTo(1.0));
              print('    食分：${magnitude.toStringAsFixed(2)}');
            }
          }
        }

        // 驗證事件按日期排序
        for (int i = 0; i < events.length - 1; i++) {
          expect(
            events[i].dateTime.isBefore(events[i + 1].dateTime) ||
            events[i].dateTime.isAtSameMomentAs(events[i + 1].dateTime),
            isTrue,
            reason: '事件應該按日期排序',
          );
        }

      } catch (e) {
        print('❌ 測試失敗：$e');
        rethrow;
      }
    });

    testWidgets('測試2024年10月的星象事件（包含月食）', (WidgetTester tester) async {
      print('\n🌙 測試2024年10月的星象事件');

      try {
        final events = await calendarService.getMonthlyEvents(
          2024,
          10,
          latitude: 25.0,
          longitude: 121.0,
        );

        print('總共找到 ${events.length} 個星象事件');

        // 篩選日月食事件
        final eclipseEvents = events.where((event) =>
          event.type == AstroEventType.eclipse
        ).toList();

        print('找到 ${eclipseEvents.length} 個日月食事件');

        // 驗證至少有一些星象事件
        expect(events.length, greaterThan(0));

        // 檢查事件類型的多樣性
        final eventTypes = events.map((e) => e.type).toSet();
        print('事件類型：${eventTypes.map((t) => t.name).join(', ')}');

        // 驗證包含日月食類型
        if (eclipseEvents.isNotEmpty) {
          expect(eventTypes, contains(AstroEventType.eclipse));
        }

      } catch (e) {
        print('❌ 測試失敗：$e');
        rethrow;
      }
    });

    test('測試日月食類型枚舉', () {
      // 測試所有日月食類型都有對應的顯示名稱
      for (final eclipseType in EclipseType.values) {
        final displayName = AstroEvent.getEclipseDisplayName(eclipseType);
        expect(displayName, isNotEmpty);
        print('${eclipseType.name}: $displayName');
      }

      // 測試所有日月食類型都有對應的圖標
      for (final eclipseType in EclipseType.values) {
        final icon = AstroEvent.getEclipseIcon(eclipseType);
        expect(icon, isNotNull);
      }

      // 測試所有日月食類型都有對應的顏色
      for (final eclipseType in EclipseType.values) {
        final color = AstroEvent.getEclipseColor(eclipseType);
        expect(color, isNotNull);
      }
    });

    test('測試日月食重要度邏輯', () {
      // 測試日全食應該是最重要的
      final solarTotalEvent = AstroEvent(
        id: 'test_solar_total',
        title: '日全食',
        description: '日全食事件',
        dateTime: DateTime.now(),
        type: AstroEventType.eclipse,
        color: AstroEvent.getEclipseColor(EclipseType.solarTotal),
        icon: AstroEvent.getEclipseIcon(EclipseType.solarTotal),
        importance: 5,
        additionalData: {'eclipseType': EclipseType.solarTotal},
      );

      expect(solarTotalEvent.importance, equals(5));
      expect(solarTotalEvent.type, equals(AstroEventType.eclipse));
    });
  });
}
