import 'package:astreal/models/astro_event.dart';
import 'package:astreal/services/astro_calendar_service.dart';
import 'package:flutter_test/flutter_test.dart';

/// 測試日月食功能
void main() {
  group('日月食功能測試', () {
    late AstroCalendarService calendarService;

    setUp(() {
      calendarService = AstroCalendarService();
    });

    test('測試日月食服務初始化', () async {
      print('🌙 測試日月食服務初始化');

      // 測試服務可以正常創建
      expect(calendarService, isNotNull);
      print('✅ 日月食服務創建成功');
    });

    test('測試日月食輔助方法', () async {
      print('\n🌙 測試日月食輔助方法');

      // 測試服務可以正常工作
      expect(calendarService, isNotNull);

      print('✅ 日月食輔助方法測試完成');
    });

    test('測試日月食類型枚舉', () {
      // 測試所有日月食類型都有對應的顯示名稱
      for (final eclipseType in EclipseType.values) {
        final displayName = AstroEvent.getEclipseDisplayName(eclipseType);
        expect(displayName, isNotEmpty);
        print('${eclipseType.name}: $displayName');
      }

      // 測試所有日月食類型都有對應的圖標
      for (final eclipseType in EclipseType.values) {
        final icon = AstroEvent.getEclipseIcon(eclipseType);
        expect(icon, isNotNull);
      }

      // 測試所有日月食類型都有對應的顏色
      for (final eclipseType in EclipseType.values) {
        final color = AstroEvent.getEclipseColor(eclipseType);
        expect(color, isNotNull);
      }
    });

    test('測試日月食重要度邏輯', () {
      // 測試日全食應該是最重要的
      final solarTotalEvent = AstroEvent(
        id: 'test_solar_total',
        title: '日全食',
        description: '日全食事件',
        dateTime: DateTime.now(),
        type: AstroEventType.eclipse,
        color: AstroEvent.getEclipseColor(EclipseType.solarTotal),
        icon: AstroEvent.getEclipseIcon(EclipseType.solarTotal),
        importance: 5,
        additionalData: {'eclipseType': EclipseType.solarTotal},
      );

      expect(solarTotalEvent.importance, equals(5));
      expect(solarTotalEvent.type, equals(AstroEventType.eclipse));
    });
  });
}
