import 'package:astreal/models/birth_data.dart';
import 'package:astreal/ui/pages/chart_selection_page.dart';
import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';

void main() {
  group('星盤導航測試', () {
    late BirthData testPerson;

    setUp(() {
      testPerson = BirthData(
        id: 'test_person_1',
        name: '測試人物',
        birthDate: DateTime(1990, 5, 15, 10, 30),
        latitude: 25.0,
        longitude: 121.0,
        birthPlace: '台北市',
      );
    });

    testWidgets('星盤選擇頁面基本顯示測試', (WidgetTester tester) async {
      // 構建星盤選擇頁面
      await tester.pumpWidget(
        MaterialApp(
          home: ChartSelectionPage(
            primaryPerson: testPerson,
            allPeople: [testPerson],
          ),
        ),
      );

      // 等待頁面渲染完成
      await tester.pumpAndSettle();

      // 驗證頁面標題
      expect(find.text('選擇星盤類型'), findsOneWidget);

      // 驗證人物信息顯示
      expect(find.text('測試人物'), findsOneWidget);
    });

    testWidgets('星盤選擇頁面導航測試', (WidgetTester tester) async {
      bool navigationCalled = false;

      // 創建一個測試用的導航函數
      void testNavigation() {
        Navigator.push(
          tester.element(find.byType(MaterialApp)),
          MaterialPageRoute(
            builder: (context) => ChartSelectionPage(
              primaryPerson: testPerson,
              allPeople: [testPerson],
            ),
          ),
        );
        navigationCalled = true;
      }

      // 構建測試頁面
      await tester.pumpWidget(
        MaterialApp(
          home: Scaffold(
            body: ElevatedButton(
              onPressed: testNavigation,
              child: const Text('測試導航'),
            ),
          ),
        ),
      );

      // 點擊按鈕
      await tester.tap(find.text('測試導航'));
      await tester.pumpAndSettle();

      // 驗證導航是否被調用
      expect(navigationCalled, isTrue);

      // 驗證是否導航到了星盤選擇頁面
      expect(find.text('選擇星盤類型'), findsOneWidget);
    });

    test('ChartSelectionPage 構造函數測試', () {
      // 測試 ChartSelectionPage 的構造函數
      final page = ChartSelectionPage(
        primaryPerson: testPerson,
        allPeople: [testPerson],
      );

      expect(page.primaryPerson, equals(testPerson));
      expect(page.allPeople, equals([testPerson]));
      expect(page.secondaryPerson, isNull);
      expect(page.initialChartType, isNull);
      expect(page.isChangingChartType, isFalse);
      expect(page.specificDate, isNull);
    });

    test('BirthData 數據驗證測試', () {
      // 驗證測試人物數據
      expect(testPerson.id, equals('test_person_1'));
      expect(testPerson.name, equals('測試人物'));
      expect(testPerson.birthPlace, equals('台北市'));
      expect(testPerson.latitude, equals(25.0));
      expect(testPerson.longitude, equals(121.0));
      expect(testPerson.birthDate.year, equals(1990));
      expect(testPerson.birthDate.month, equals(5));
      expect(testPerson.birthDate.day, equals(15));
    });

    testWidgets('快捷按鈕點擊測試', (WidgetTester tester) async {
      bool buttonClicked = false;

      // 創建一個模擬的快捷按鈕
      Widget buildQuickActionButton({
        required IconData icon,
        required String title,
        required VoidCallback onTap,
      }) {
        return InkWell(
          onTap: onTap,
          child: Container(
            padding: const EdgeInsets.all(12),
            child: Column(
              children: [
                Icon(icon, size: 18),
                const SizedBox(height: 4),
                Text(title, style: const TextStyle(fontSize: 12)),
              ],
            ),
          ),
        );
      }

      await tester.pumpWidget(
        MaterialApp(
          home: Scaffold(
            body: buildQuickActionButton(
              icon: Icons.star,
              title: '星盤',
              onTap: () {
                buttonClicked = true;
              },
            ),
          ),
        ),
      );

      // 點擊按鈕
      await tester.tap(find.byType(InkWell));
      await tester.pump();

      // 驗證點擊事件
      expect(buttonClicked, isTrue);
    });

    test('導航邏輯測試', () {
      // 測試導航邏輯的各種情況
      
      // 情況1：有選擇人物
      BirthData? selectedPerson = testPerson;
      bool shouldNavigate = selectedPerson != null;
      expect(shouldNavigate, isTrue);

      // 情況2：沒有選擇人物
      selectedPerson = null;
      shouldNavigate = selectedPerson != null;
      expect(shouldNavigate, isFalse);

      // 情況3：人物列表不為空
      List<BirthData> birthDataList = [testPerson];
      expect(birthDataList.isNotEmpty, isTrue);

      // 情況4：人物列表為空
      birthDataList = [];
      expect(birthDataList.isEmpty, isTrue);
    });

    testWidgets('SnackBar 提示測試', (WidgetTester tester) async {
      // 測試當沒有選擇人物時顯示提示
      await tester.pumpWidget(
        MaterialApp(
          home: Scaffold(
            body: Builder(
              builder: (context) => ElevatedButton(
                onPressed: () {
                  ScaffoldMessenger.of(context).showSnackBar(
                    const SnackBar(
                      content: Text('請先選擇一個人物以查看星盤'),
                      duration: Duration(seconds: 2),
                    ),
                  );
                },
                child: const Text('顯示提示'),
              ),
            ),
          ),
        ),
      );

      // 點擊按鈕顯示 SnackBar
      await tester.tap(find.text('顯示提示'));
      await tester.pump();

      // 驗證 SnackBar 顯示
      expect(find.text('請先選擇一個人物以查看星盤'), findsOneWidget);
    });

    test('圖標和顏色常量測試', () {
      // 驗證星盤按鈕使用的圖標和顏色
      expect(Icons.star, isNotNull);
      expect(Icons.auto_awesome, isNotNull);
      expect(Icons.calendar_month, isNotNull);

      // 驗證文本常量
      const chartTitle = '星盤';
      const chartSubtitle = '各種星盤類型';
      
      expect(chartTitle.isNotEmpty, isTrue);
      expect(chartSubtitle.isNotEmpty, isTrue);
      expect(chartTitle.length, greaterThan(0));
      expect(chartSubtitle.length, greaterThan(0));
    });
  });
}
