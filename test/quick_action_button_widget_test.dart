import 'package:astreal/ui/AppTheme.dart';
import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';

/// 測試用的快捷按鈕組件
class QuickActionButton extends StatelessWidget {
  final IconData icon;
  final String title;
  final String subtitle;
  final Color color;
  final VoidCallback onTap;

  const QuickActionButton({
    super.key,
    required this.icon,
    required this.title,
    required this.subtitle,
    required this.color,
    required this.onTap,
  });

  @override
  Widget build(BuildContext context) {
    return InkWell(
      onTap: onTap,
      borderRadius: BorderRadius.circular(12),
      child: Container(
        padding: const EdgeInsets.all(12),
        decoration: BoxDecoration(
          color: color.withValues(alpha: 0.1),
          borderRadius: BorderRadius.circular(10),
          border: Border.all(
            color: color.withValues(alpha: 0.3),
            width: 1,
          ),
        ),
        child: Column(
          children: [
            Container(
              width: 32,
              height: 32,
              decoration: BoxDecoration(
                color: color,
                borderRadius: BorderRadius.circular(16),
              ),
              child: Icon(
                icon,
                color: Colors.white,
                size: 18,
              ),
            ),
            const SizedBox(height: 4),
            Text(
              title,
              style: TextStyle(
                fontSize: 12,
                fontWeight: FontWeight.bold,
                color: color,
              ),
              textAlign: TextAlign.center,
              maxLines: 1,
              overflow: TextOverflow.ellipsis,
            ),
            const SizedBox(height: 1),
            Text(
              subtitle,
              style: TextStyle(
                fontSize: 9,
                color: Colors.grey[600],
              ),
              textAlign: TextAlign.center,
              maxLines: 1,
              overflow: TextOverflow.ellipsis,
            ),
          ],
        ),
      ),
    );
  }
}

void main() {
  group('快捷按鈕組件測試', () {
    testWidgets('快捷按鈕基本顯示測試', (WidgetTester tester) async {
      bool buttonTapped = false;

      // 構建快捷按鈕
      await tester.pumpWidget(
        MaterialApp(
          home: Scaffold(
            body: QuickActionButton(
              icon: Icons.auto_awesome,
              title: '卜卦分析',
              subtitle: '占星、周易、塔羅',
              color: AppColors.solarAmber,
              onTap: () {
                buttonTapped = true;
              },
            ),
          ),
        ),
      );

      // 驗證按鈕元素
      expect(find.text('卜卦分析'), findsOneWidget);
      expect(find.text('占星、周易、塔羅'), findsOneWidget);
      expect(find.byIcon(Icons.auto_awesome), findsOneWidget);

      // 測試點擊功能
      await tester.tap(find.byType(InkWell));
      expect(buttonTapped, isTrue);
    });

    testWidgets('星象日曆按鈕顯示測試', (WidgetTester tester) async {
      bool buttonTapped = false;

      // 構建星象日曆按鈕
      await tester.pumpWidget(
        MaterialApp(
          home: Scaffold(
            body: QuickActionButton(
              icon: Icons.calendar_month,
              title: '星象日曆',
              subtitle: '月相、節氣、相位',
              color: AppColors.royalIndigo,
              onTap: () {
                buttonTapped = true;
              },
            ),
          ),
        ),
      );

      // 驗證按鈕元素
      expect(find.text('星象日曆'), findsOneWidget);
      expect(find.text('月相、節氣、相位'), findsOneWidget);
      expect(find.byIcon(Icons.calendar_month), findsOneWidget);

      // 測試點擊功能
      await tester.tap(find.byType(InkWell));
      expect(buttonTapped, isTrue);
    });

    testWidgets('快捷按鈕佈局測試', (WidgetTester tester) async {
      // 構建三個按鈕的佈局
      await tester.pumpWidget(
        MaterialApp(
          home: Scaffold(
            body: Padding(
              padding: const EdgeInsets.all(16),
              child: Row(
                children: [
                  Expanded(
                    child: QuickActionButton(
                      icon: Icons.auto_awesome,
                      title: '卜卦分析',
                      subtitle: '占星、周易、塔羅',
                      color: AppColors.solarAmber,
                      onTap: () {},
                    ),
                  ),
                  const SizedBox(width: 6),
                  Expanded(
                    child: QuickActionButton(
                      icon: Icons.star,
                      title: '星盤',
                      subtitle: '各種星盤類型',
                      color: AppColors.success,
                      onTap: () {},
                    ),
                  ),
                  const SizedBox(width: 6),
                  Expanded(
                    child: QuickActionButton(
                      icon: Icons.calendar_month,
                      title: '星象日曆',
                      subtitle: '月相、節氣、相位',
                      color: AppColors.royalIndigo,
                      onTap: () {},
                    ),
                  ),
                ],
              ),
            ),
          ),
        ),
      );

      // 驗證三個按鈕都存在
      expect(find.text('卜卦分析'), findsOneWidget);
      expect(find.text('星盤'), findsOneWidget);
      expect(find.text('星象日曆'), findsOneWidget);
      expect(find.byType(QuickActionButton), findsNWidgets(3));
    });

    testWidgets('快捷按鈕樣式測試', (WidgetTester tester) async {
      // 構建按鈕
      await tester.pumpWidget(
        MaterialApp(
          home: Scaffold(
            body: QuickActionButton(
              icon: Icons.auto_awesome,
              title: '卜卦分析',
              subtitle: '占星、周易、塔羅',
              color: AppColors.solarAmber,
              onTap: () {},
            ),
          ),
        ),
      );

      // 驗證容器存在
      expect(find.byType(Container), findsWidgets);

      // 驗證圓形圖標容器
      final iconContainers = find.descendant(
        of: find.byType(QuickActionButton),
        matching: find.byType(Container),
      );
      expect(iconContainers, findsWidgets);

      // 驗證文本樣式
      final titleText = tester.widget<Text>(find.text('卜卦分析'));
      expect(titleText.style?.fontSize, equals(12));
      expect(titleText.style?.fontWeight, equals(FontWeight.bold));

      final subtitleText = tester.widget<Text>(find.text('占星、周易、塔羅'));
      expect(subtitleText.style?.fontSize, equals(9));
    });

    test('快捷按鈕配置常量測試', () {
      // 測試圖標常量
      expect(Icons.auto_awesome, isNotNull);
      expect(Icons.star, isNotNull);
      expect(Icons.calendar_month, isNotNull);

      // 測試顏色常量
      expect(AppColors.solarAmber, isNotNull);
      expect(AppColors.success, isNotNull);
      expect(AppColors.royalIndigo, isNotNull);

      // 測試文本常量
      const divinationTitle = '卜卦分析';
      const divinationSubtitle = '占星、周易、塔羅';
      const chartTitle = '星盤';
      const chartSubtitle = '各種星盤類型';
      const calendarTitle = '星象日曆';
      const calendarSubtitle = '月相、節氣、相位';

      expect(divinationTitle.length, greaterThan(0));
      expect(divinationSubtitle.length, greaterThan(0));
      expect(chartTitle.length, greaterThan(0));
      expect(chartSubtitle.length, greaterThan(0));
      expect(calendarTitle.length, greaterThan(0));
      expect(calendarSubtitle.length, greaterThan(0));
    });
  });
}
