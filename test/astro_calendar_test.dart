import 'package:astreal/models/astro_event.dart';
import 'package:astreal/services/astro_calendar_service.dart';
import 'package:flutter_test/flutter_test.dart';

void main() {
  group('星象日曆服務測試', () {
    late AstroCalendarService service;

    setUp(() {
      service = AstroCalendarService();
    });

    test('獲取月度事件', () async {
      // 測試獲取2024年1月的事件
      final events = await service.getMonthlyEvents(2024, 1);
      
      // 驗證返回的事件列表不為空
      expect(events, isNotNull);
      expect(events, isA<List<AstroEvent>>());
      
      // 打印事件數量以供調試
      print('2024年1月事件數量: ${events.length}');
      
      // 驗證事件是否按日期排序
      if (events.length > 1) {
        for (int i = 0; i < events.length - 1; i++) {
          expect(
            events[i].dateTime.isBefore(events[i + 1].dateTime) ||
            events[i].dateTime.isAtSameMomentAs(events[i + 1].dateTime),
            isTrue,
            reason: '事件應該按日期排序',
          );
        }
      }
    });

    test('獲取日度事件', () async {
      // 測試獲取2024年1月1日的事件
      final date = DateTime(2024, 1, 1);
      final events = await service.getDailyEvents(date);
      
      // 驗證返回的事件列表不為空
      expect(events, isNotNull);
      expect(events, isA<List<AstroEvent>>());
      
      // 驗證所有事件都在指定日期
      for (final event in events) {
        expect(event.dateTime.year, equals(date.year));
        expect(event.dateTime.month, equals(date.month));
        expect(event.dateTime.day, equals(date.day));
      }
      
      print('2024年1月1日事件數量: ${events.length}');
    });

    test('事件類型驗證', () async {
      final events = await service.getMonthlyEvents(2024, 1);
      
      // 驗證事件類型是否有效
      for (final event in events) {
        expect(event.type, isA<AstroEventType>());
        expect(event.title, isNotEmpty);
        expect(event.description, isNotEmpty);
        expect(event.importance, greaterThanOrEqualTo(1));
        expect(event.importance, lessThanOrEqualTo(5));
      }
    });

    test('月相事件測試', () async {
      final events = await service.getMonthlyEvents(2024, 1);
      
      // 查找月相事件
      final moonPhaseEvents = events.where(
        (event) => event.type == AstroEventType.moonPhase
      ).toList();
      
      print('月相事件數量: ${moonPhaseEvents.length}');
      
      // 驗證月相事件的屬性
      for (final event in moonPhaseEvents) {
        expect(event.additionalData, isNotNull);
        expect(event.additionalData!['moonPhase'], isNotNull);
      }
    });

    test('節氣事件測試', () async {
      final events = await service.getMonthlyEvents(2024, 3); // 3月應該有春分
      
      // 查找節氣事件
      final seasonEvents = events.where(
        (event) => event.type == AstroEventType.seasonChange
      ).toList();
      
      print('節氣事件數量: ${seasonEvents.length}');
      
      // 驗證節氣事件的屬性
      for (final event in seasonEvents) {
        expect(event.importance, equals(5)); // 節氣應該是最高重要度
        expect(event.additionalData, isNotNull);
        expect(event.additionalData!['seasonType'], isNotNull);
      }
    });
  });
}
