import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';

void main() {
  group('星盤按鈕調試測試', () {
    testWidgets('基本按鈕點擊測試', (WidgetTester tester) async {
      bool buttonClicked = false;
      String clickedTitle = '';

      // 模擬快捷按鈕的構建方法
      Widget buildQuickActionButton({
        required IconData icon,
        required String title,
        required String subtitle,
        required Color color,
        required VoidCallback onTap,
      }) {
        return InkWell(
          onTap: () {
            print('🎯 快捷按鈕被點擊: $title');
            clickedTitle = title;
            onTap();
          },
          borderRadius: BorderRadius.circular(10),
          child: Container(
            padding: const EdgeInsets.all(12),
            decoration: BoxDecoration(
              color: color.withValues(alpha: 0.1),
              borderRadius: BorderRadius.circular(10),
              border: Border.all(
                color: color.withValues(alpha: 0.3),
                width: 1,
              ),
            ),
            child: Column(
              children: [
                Container(
                  width: 32,
                  height: 32,
                  decoration: BoxDecoration(
                    color: color,
                    borderRadius: BorderRadius.circular(16),
                  ),
                  child: Icon(
                    icon,
                    color: Colors.white,
                    size: 18,
                  ),
                ),
                const SizedBox(height: 4),
                Text(
                  title,
                  style: TextStyle(
                    fontSize: 12,
                    fontWeight: FontWeight.bold,
                    color: color,
                  ),
                  textAlign: TextAlign.center,
                  maxLines: 1,
                  overflow: TextOverflow.ellipsis,
                ),
                const SizedBox(height: 1),
                Text(
                  subtitle,
                  style: TextStyle(
                    fontSize: 9,
                    color: Colors.grey[600],
                  ),
                  textAlign: TextAlign.center,
                  maxLines: 1,
                  overflow: TextOverflow.ellipsis,
                ),
              ],
            ),
          ),
        );
      }

      // 構建測試頁面
      await tester.pumpWidget(
        MaterialApp(
          home: Scaffold(
            body: Center(
              child: buildQuickActionButton(
                icon: Icons.star,
                title: '星盤',
                subtitle: '各種星盤類型',
                color: Colors.green,
                onTap: () {
                  buttonClicked = true;
                  print('🌟 星盤按鈕被點擊了！');
                },
              ),
            ),
          ),
        ),
      );

      // 驗證按鈕顯示
      expect(find.text('星盤'), findsOneWidget);
      expect(find.text('各種星盤類型'), findsOneWidget);
      expect(find.byIcon(Icons.star), findsOneWidget);

      // 點擊按鈕
      await tester.tap(find.byType(InkWell));
      await tester.pump();

      // 驗證點擊事件
      expect(buttonClicked, isTrue);
      expect(clickedTitle, equals('星盤'));
    });

    testWidgets('三個按鈕佈局測試', (WidgetTester tester) async {
      List<String> clickedButtons = [];

      Widget buildQuickActionButton({
        required IconData icon,
        required String title,
        required String subtitle,
        required Color color,
        required VoidCallback onTap,
      }) {
        return Expanded(
          child: InkWell(
            onTap: () {
              clickedButtons.add(title);
              onTap();
            },
            child: Container(
              padding: const EdgeInsets.all(12),
              child: Column(
                children: [
                  Icon(icon, size: 18, color: color),
                  const SizedBox(height: 4),
                  Text(title, style: const TextStyle(fontSize: 12)),
                ],
              ),
            ),
          ),
        );
      }

      // 構建三個按鈕的佈局
      await tester.pumpWidget(
        MaterialApp(
          home: Scaffold(
            body: Padding(
              padding: const EdgeInsets.all(16),
              child: Row(
                children: [
                  buildQuickActionButton(
                    icon: Icons.auto_awesome,
                    title: '卜卦分析',
                    subtitle: '占星、周易、塔羅',
                    color: Colors.amber,
                    onTap: () => print('卜卦分析被點擊'),
                  ),
                  const SizedBox(width: 6),
                  buildQuickActionButton(
                    icon: Icons.star,
                    title: '星盤',
                    subtitle: '各種星盤類型',
                    color: Colors.green,
                    onTap: () => print('星盤被點擊'),
                  ),
                  const SizedBox(width: 6),
                  buildQuickActionButton(
                    icon: Icons.calendar_month,
                    title: '星象日曆',
                    subtitle: '月相、節氣、相位',
                    color: Colors.indigo,
                    onTap: () => print('星象日曆被點擊'),
                  ),
                ],
              ),
            ),
          ),
        ),
      );

      // 驗證三個按鈕都存在
      expect(find.text('卜卦分析'), findsOneWidget);
      expect(find.text('星盤'), findsOneWidget);
      expect(find.text('星象日曆'), findsOneWidget);

      // 點擊星盤按鈕
      await tester.tap(find.text('星盤'));
      await tester.pump();

      // 驗證點擊記錄
      expect(clickedButtons.contains('星盤'), isTrue);
    });

    test('導航邏輯模擬測試', () {
      // 模擬導航邏輯
      bool hasSelectedPerson = false;
      bool showSnackBar = false;
      bool didNavigate = false;

      void simulateChartNavigation() {
        print('🌟 星盤按鈕被點擊了！');

        if (!hasSelectedPerson) {
          print('🌟 沒有選擇人物，顯示提示');
          showSnackBar = true;
          return;
        }

        print('🌟 開始導航到星盤選擇頁面');
        didNavigate = true;
        print('🌟 導航成功');
      }

      // 測試沒有選擇人物的情況
      simulateChartNavigation();
      expect(showSnackBar, isTrue);
      expect(didNavigate, isFalse);

      // 重置狀態
      showSnackBar = false;
      didNavigate = false;

      // 測試有選擇人物的情況
      hasSelectedPerson = true;
      simulateChartNavigation();
      expect(showSnackBar, isFalse);
      expect(didNavigate, isTrue);
    });

    test('按鈕配置驗證', () {
      // 驗證星盤按鈕的配置
      const chartButtonConfig = {
        'icon': Icons.star,
        'title': '星盤',
        'subtitle': '各種星盤類型',
        'color': Colors.green,
      };

      expect(chartButtonConfig['icon'], equals(Icons.star));
      expect(chartButtonConfig['title'], equals('星盤'));
      expect(chartButtonConfig['subtitle'], equals('各種星盤類型'));
      expect(chartButtonConfig['color'], equals(Colors.green));
    });

    testWidgets('按鈕視覺效果測試', (WidgetTester tester) async {
      // 構建按鈕
      await tester.pumpWidget(
        MaterialApp(
          home: Scaffold(
            body: InkWell(
              onTap: () {},
              child: Container(
                width: 100,
                height: 100,
                decoration: BoxDecoration(
                  color: Colors.green.withValues(alpha: 0.1),
                  borderRadius: BorderRadius.circular(10),
                  border: Border.all(
                    color: Colors.green.withValues(alpha: 0.3),
                    width: 1,
                  ),
                ),
                child: const Column(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    Icon(Icons.star, color: Colors.white, size: 18),
                    SizedBox(height: 4),
                    Text('星盤', style: TextStyle(fontSize: 12)),
                  ],
                ),
              ),
            ),
          ),
        ),
      );

      // 驗證視覺元素
      expect(find.byType(InkWell), findsOneWidget);
      expect(find.byType(Container), findsWidgets);
      expect(find.byIcon(Icons.star), findsOneWidget);
      expect(find.text('星盤'), findsOneWidget);
    });
  });
}
